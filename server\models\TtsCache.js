const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');

class TtsCache {
  /**
   * 计算文本的字符数量
   * 一个汉字算一个字符，标点符号算一个字符
   * @param {string} text - 要计算的文本
   * @returns {number} 字符数量
   */
  static countCharacters(text) {
    // 使用字符串长度即可，因为JavaScript字符串按Unicode处理
    // 一个汉字、一个字母、一个标点符号都算一个字符
    return text ? text.length : 0;
  }

  /**
   * 计算费用
   * 每万字符5元，zh-CN-XiaoxiaoNeural语音不收费
   * @param {number} characterCount - 字符数量
   * @param {string} voice - 使用的语音
   * @returns {number} 费用(元)
   */
  static calculateCost(characterCount, voice) {
    // 如果是zh-CN-XiaoxiaoNeural语音，费用为0
    if (voice === 'zh-CN-XiaoxiaoNeural' || voice === 'en-US-AriaNeural' || voice === 'ja-JP-NanamiNeural') {
      return 0;
    }
    
    // 每万字符5元
    const costPer10k = 5;
    return Number(((characterCount / 10000) * costPer10k).toFixed(2));
  }

  /**
   * 创建TTS缓存
   * @param {Object} cacheData - 缓存数据
   * @param {string} cacheData.text - 原始文本内容
   * @param {string} cacheData.voice - 使用的语音
   * @param {Buffer} cacheData.audioData - 音频文件数据(二进制)
   * @param {number} [cacheData.ttl=259200] - 生存时间(秒)，默认3天
   * @param {string} [cacheData.sessionId] - 关联的会话ID
   * @param {string} [cacheData.userId] - 关联的用户ID
   * @param {string} [cacheData.messageId] - 关联的消息ID，用于段落级缓存
   * @returns {Promise<Object>} 创建的缓存记录
   */
  static async create(cacheData) {
    const pool = await getConnection();
    const id = uuidv4();
    const now = new Date();

    // 计算过期时间
    let ttl = cacheData.ttl || 259200; // 默认3天（259200秒）

    // 对于未登录用户，强制设置24小时过期时间
    if (!cacheData.userId) {
      ttl = 86400; // 24小时（86400秒）
      // console.log(`[TtsCache] 未登录用户语音缓存，设置24小时过期时间: sessionId=${cacheData.sessionId || 'N/A'}`);
    }

    const expiresAt = new Date(now.getTime() + ttl * 1000);
    
    // 计算字符数和费用
    const characterCount = this.countCharacters(cacheData.text);
    const cost = this.calculateCost(characterCount, cacheData.voice);

    // console.log(`[TtsCache] 创建TTS缓存记录: id=${id}, sessionId=${cacheData.sessionId || 'N/A'}, messageId=${cacheData.messageId || 'N/A'}, ttl=${ttl}秒, 字符数=${characterCount}, 费用=${cost}元`);
    
    // 检查是否需要添加新的数据库字段
    let hasMessageIdColumn = false;
    let hasCharacterCountColumn = false;
    let hasCostColumn = false;
    
    try {
      // 检查表结构
      const [columns] = await pool.query(`SHOW COLUMNS FROM tts_cache`);
      const columnNames = columns.map(col => col.Field);
      
      hasMessageIdColumn = columnNames.includes('message_id');
      hasCharacterCountColumn = columnNames.includes('character_count');
      hasCostColumn = columnNames.includes('cost');
      
      // 如果缺少必要的列，添加它们
      if (!hasMessageIdColumn) {
        // console.log(`[TtsCache] 添加message_id列到tts_cache表`);
        await pool.query(`ALTER TABLE tts_cache ADD COLUMN message_id VARCHAR(36) NULL COMMENT '关联的消息ID'`);
        await pool.query(`ALTER TABLE tts_cache ADD INDEX idx_message_id (message_id)`);
        hasMessageIdColumn = true;
      }
      
      if (!hasCharacterCountColumn) {
        // console.log(`[TtsCache] 添加character_count列到tts_cache表`);
        await pool.query(`ALTER TABLE tts_cache ADD COLUMN character_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '文本字符数量'`);
        await pool.query(`ALTER TABLE tts_cache ADD INDEX idx_character_count (character_count)`);
        hasCharacterCountColumn = true;
      }
      
      if (!hasCostColumn) {
        // console.log(`[TtsCache] 添加cost列到tts_cache表`);
        await pool.query(`ALTER TABLE tts_cache ADD COLUMN cost DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '生成费用(元)'`);
        await pool.query(`ALTER TABLE tts_cache ADD INDEX idx_cost (cost)`);
        hasCostColumn = true;
      }
    } catch (error) {
      // console.error(`[TtsCache] 检查表结构时出错:`, error);
      // 继续执行，尝试插入数据
    }
    
    // 构建SQL插入语句
    let sql = `INSERT INTO tts_cache (
      id, text, voice, audio_data, 
      created_at, expires_at, session_id, user_id`;
    
    if (hasMessageIdColumn) {
      sql += `, message_id`;
    }
    
    if (hasCharacterCountColumn) {
      sql += `, character_count`;
    }
    
    if (hasCostColumn) {
      sql += `, cost`;
    }
    
    sql += `) VALUES (?, ?, ?, ?, ?, ?, ?, ?`;
    
    if (hasMessageIdColumn) {
      sql += `, ?`;
    }
    
    if (hasCharacterCountColumn) {
      sql += `, ?`;
    }
    
    if (hasCostColumn) {
      sql += `, ?`;
    }
    
    sql += `)`;
    
    // 构建参数数组
    const params = [
      id,
      cacheData.text,
      cacheData.voice,
      cacheData.audioData,
      now,
      expiresAt,
      cacheData.sessionId || null,
      cacheData.userId || null
    ];
    
    if (hasMessageIdColumn) {
      params.push(cacheData.messageId || null);
    }
    
    if (hasCharacterCountColumn) {
      params.push(characterCount);
    }
    
    if (hasCostColumn) {
      params.push(cost);
    }
    
    const [result] = await pool.query(sql, params);

    if (result.affectedRows > 0) {
      // console.log(`[TtsCache] 成功创建TTS缓存记录: id=${id}, expires_at=${expiresAt.toISOString()}, 字符数=${characterCount}, 费用=${cost}元`);
    } else {
      // console.error(`[TtsCache] 创建TTS缓存记录失败: id=${id}`);
    }

    return { 
      id,
      ...cacheData, 
      created_at: now, 
      expires_at: expiresAt,
      character_count: characterCount,
      cost: cost
    };
  }

  /**
   * 解析缓存键，提取sessionId和messageId
   * @param {string} cacheKey - 缓存键字符串
   * @returns {Object} 包含sessionId和messageId的对象
   */
  static parseCacheKey(cacheKey) {
    let sessionId = null;
    let messageId = null;
    
    // 处理格式：sessionId_板块类型_para_x
    if (cacheKey.includes('_para_')) {
      // 解析新格式
      const parts = cacheKey.split('_');
      if (parts.length >= 4) {
        sessionId = parts[0];
        // 板块类型_para_x 格式，例如：base_para_0 或 follow_para_2
        const sectionType = parts.length > 4 ? parts[1] : 'base'; // 默认为base
        const paraIndex = parts[parts.length - 1];
        messageId = `${sectionType}_para_${paraIndex}`;
        // console.log(`[TtsCache] 提取新格式: sessionId=${sessionId}, sectionType=${sectionType}, paraIndex=${paraIndex}, messageId=${messageId}`);
      }
    }
    // 处理包含paragraph但不包含para_的格式，转换为新格式
    else if (cacheKey.includes('_paragraph_')) {
      const parts = cacheKey.split('_');
      sessionId = parts[0];
      
      // 提取段落索引
      let paragraphIndex = '0'; // 默认索引
      const paraMatch = cacheKey.match(/_paragraph_(\d+)/);
      if (paraMatch) {
        paragraphIndex = paraMatch[1];
      }
      
      // 提取板块类型
      let sectionType = 'base'; // 默认板块类型
      if (cacheKey.includes('_base_') || cacheKey.includes('/base/')) {
        sectionType = 'base'; // 基础解读板块
      } else if (cacheKey.includes('_follow_') || cacheKey.includes('/follow/')) {
        sectionType = 'follow'; // 追问板块
      }
      
      // 构建新的消息ID格式
      messageId = `${sectionType}_para_${paragraphIndex}`;
      // console.log(`[TtsCache] 从paragraph格式转换为新格式: sessionId=${sessionId}, 板块=${sectionType}, 索引=${paragraphIndex}, messageId=${messageId}`);
    }
    
    return { sessionId, messageId };
  }
  
  /**
   * 通过缓存键和语音类型查找缓存
   * @param {string} cacheKey - 缓存键或包含缓存键的格式化字符串
   * @param {string} voice - 语音类型
   * @returns {Promise<Object|null>} 缓存记录或null
   */
  static async findByKey(cacheKey, voice) {
    // 检查是否为格式化字符串格式：key:text
    const keyMatch = cacheKey.match(/^([^:]+):(.+)$/);
    let key = cacheKey;
    
    if (keyMatch) {
      // 如果是格式化字符串，提取缓存键部分
      key = keyMatch[1];
      // console.log(`[TtsCache] 从格式化字符串提取缓存键: ${key}`);
    }
    
    // 解析缓存键以提取sessionId和messageId
    const { sessionId, messageId } = this.parseCacheKey(key);
    
    // 如果能够提取sessionId和messageId，则使用它们查询
    if (sessionId && messageId) {
      console.log(`[TtsCache] 使用解析的sessionId和messageId查询: sessionId=${sessionId}, messageId=${messageId}`);
      return await this.findBySessionAndMessageId(sessionId, messageId);
    }
    
    console.log(`[TtsCache] 无法从缓存键解析sessionId和messageId，无法查询缓存`);
    return null;
  }

  /**
   * 通过会话ID和消息ID查找缓存
   * @param {string} sessionId - 会话ID
   * @param {string} messageId - 消息ID
   * @returns {Promise<Object|null>} 缓存记录或null
   */
  static async findBySessionAndMessageId(sessionId, messageId) {
    if (!sessionId || !messageId) {
      // console.log(`[TtsCache] 查询缺少必要参数: sessionId=${sessionId || 'null'}, messageId=${messageId || 'null'}`);
      return null;
    }
    
    // console.log(`[TtsCache] 查询音频缓存: sessionId=${sessionId}, messageId=${messageId}`);
    
    // 确保消息ID是新格式 {板块}_para_{索引}
    let normalizedMessageId = messageId;
    
    // 如果需要，在这里规范化messageId
    if (!messageId.includes('_para_')) {
      // 不是新格式，提取索引部分，默认使用base板块
      const numMatch = messageId.match(/(\d+)$/);
      const index = numMatch ? numMatch[1] : '0';
      normalizedMessageId = `base_para_${index}`;
      // console.log(`[TtsCache] 规范化messageId: ${messageId} -> ${normalizedMessageId}`);
    }
    
    const pool = await getConnection();
    
    // 先检查表中是否有message_id列
    let hasMessageIdColumn = false;
    try {
      const [columns] = await pool.query(`SHOW COLUMNS FROM tts_cache`);
      hasMessageIdColumn = columns.some(col => col.Field === 'message_id');
      
      if (!hasMessageIdColumn) {
        // console.log(`[TtsCache] tts_cache表中没有message_id列，无法查询`);
        return null;
      }
    } catch (error) {
      console.error(`[TtsCache] 检查表结构时出错:`, error); 
      return null;
    }
    
    // console.log(`[TtsCache] 执行查询: sessionId=${sessionId}, messageId=${normalizedMessageId}`);
    
    // 只使用精确匹配，移除所有模糊匹配逻辑
    const [rows] = await pool.query(
      `SELECT * FROM tts_cache 
       WHERE session_id = ? AND message_id = ? AND expires_at > NOW()
       ORDER BY created_at DESC
       LIMIT 1`,
      [sessionId, normalizedMessageId]
    );
    
    if (rows.length > 0) {
      const cache = rows[0];
      // console.log(`[TtsCache] 查询成功: id=${cache.id}, 创建时间=${new Date(cache.created_at).toISOString()}, 过期时间=${new Date(cache.expires_at).toISOString()}, 音频大小=${cache.audio_data ? cache.audio_data.length : 0}字节`);
      return cache;
    }
    
    // console.log(`[TtsCache] 未找到匹配的缓存记录: sessionId=${sessionId}, messageId=${normalizedMessageId}`);
    return null;
  }

  /**
   * 通过会话ID查找该会话的所有缓存
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Array>} 缓存记录数组
   */
  static async findBySessionId(sessionId) {
    const pool = await getConnection();
    
    let sql = `SELECT id, text, voice, created_at, expires_at, session_id, user_id, message_id 
               FROM tts_cache WHERE session_id = ?
               ORDER BY created_at DESC`;
    
    const [rows] = await pool.query(sql, [sessionId]);
    
    return rows;
  }

  /**
   * 通过用户ID查找该用户的所有缓存
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @param {number} [options.limit=100] - 每页数量
   * @param {number} [options.offset=0] - 偏移量
   * @returns {Promise<Object>} 包含数据和分页信息的对象
   */
  static async findByUserId(userId, options = {}) {
    const pool = await getConnection();
    const limit = options.limit || 100;
    const offset = options.offset || 0;
    
    // 查询总数
    const [countRows] = await pool.query(
      `SELECT COUNT(*) as total FROM tts_cache WHERE user_id = ?`,
      [userId]
    );
    
    const total = countRows[0].total;
    
    // 查询数据
    const [rows] = await pool.query(
      `SELECT id, text, voice, created_at, expires_at, session_id, user_id
       FROM tts_cache 
       WHERE user_id = ? 
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [userId, limit, offset]
    );
    
    return {
      data: rows,
      total,
      limit,
      offset
    };
  }

  /**
   * 获取指定ID的音频数据
   * @param {string} id - 缓存ID
   * @returns {Promise<Buffer|null>} 音频数据或null
   */
  static async getAudioById(id) {
    const pool = await getConnection();
    
    const [rows] = await pool.query(
      `SELECT audio_data FROM tts_cache WHERE id = ? AND expires_at > NOW()`,
      [id]
    );
    
    return rows.length > 0 ? rows[0].audio_data : null;
  }
  
  /**
   * 清理过期的缓存记录
   * @returns {Promise<number>} 删除的记录数
   */
  static async cleanExpired() {
    const pool = await getConnection();
    
    // 移除重复的日志输出
    // console.log(`[TtsCache] 执行数据库清理过期记录操作...`);
    const startTime = Date.now();
    
    try {
      // 先获取将被删除的记录ID，用于记录
      const [idsToDelete] = await pool.query(
        `SELECT id FROM tts_cache WHERE expires_at < NOW() LIMIT 1000`
      );
      
      if (idsToDelete.length === 0) {
        // 移除日志输出
        // console.log(`[TtsCache] 没有找到过期记录需要清理`);
        return 0;
      }
      
      // 移除日志输出
      // console.log(`[TtsCache] 将删除 ${idsToDelete.length} 条过期记录`);
      
      // 执行删除操作
      const [result] = await pool.query(
        `DELETE FROM tts_cache WHERE expires_at < NOW()`
      );
      
      // 移除日志输出
      // const elapsedTime = Date.now() - startTime;
      const affectedRows = result && result.affectedRows ? result.affectedRows : 0;
      // console.log(`[TtsCache] 数据库删除操作完成，影响 ${affectedRows} 行，耗时 ${elapsedTime}ms`);
      
      return affectedRows;
    } catch (error) {
      console.error(`[TtsCache] 数据库清理操作失败:`, error);
      return 0; // 发生错误时返回0而不是抛出异常，避免影响上游调用
    }
  }
  
  /**
   * 清理非VIP用户的过期缓存记录
   * 只清理非VIP用户的音频缓存，VIP用户缓存不会被清理
   * @returns {Promise<number>} 删除的记录数
   */
  static async cleanExpiredForNonVipUsers() {
    const pool = await getConnection();
    
    // 移除重复的日志输出
    // console.log(`[TtsCache] 执行数据库清理非VIP用户的过期记录操作...`);
    const startTime = Date.now();
    
    try {
      // 先获取非VIP用户将被删除的记录数量，用于日志记录
      const [countQuery] = await pool.query(`
        SELECT COUNT(*) as count 
        FROM tts_cache tc
        LEFT JOIN users u ON tc.user_id = u.id
        WHERE tc.expires_at < NOW() AND (
          u.id IS NULL OR 
          u.vip_status IS NULL OR 
          u.vip_status = 'none' OR 
          (u.vip_status != 'none' AND u.vip_end_date < NOW())
        )
        LIMIT 1000
      `);
      
      const countToDelete = countQuery[0]?.count || 0;
      
      if (countToDelete === 0) {
        // 移除日志输出，保持一致性
        // console.log(`[TtsCache] 没有找到非VIP用户的过期记录需要清理`);
        return 0;
      }
      
      // 先更新已过期的VIP用户状态
      await pool.query(`
        UPDATE users
        SET vip_status = 'expired'
        WHERE vip_status = 'active' AND vip_end_date < NOW()
      `);
      
      // 执行删除操作 - 删除非VIP用户的过期记录
      // 这包括：无用户ID的记录、用户不存在的记录、非VIP用户的记录、VIP已过期的用户记录
      const [result] = await pool.query(`
        DELETE tc FROM tts_cache tc
        LEFT JOIN users u ON tc.user_id = u.id
        WHERE tc.expires_at < NOW() AND (
          u.id IS NULL OR 
          u.vip_status IS NULL OR 
          u.vip_status = 'none' OR 
          u.vip_status = 'expired'
        )
      `);
      
      const affectedRows = result && result.affectedRows ? result.affectedRows : 0;
      
      return affectedRows;
    } catch (error) {
      console.error(`[TtsCache] 数据库清理非VIP用户操作失败:`, error);
      return 0; // 发生错误时返回0而不是抛出异常，避免影响上游调用
    }
  }
  
  /**
   * 设置或更新缓存记录的过期时间
   * @param {string} id - 缓存ID
   * @param {number} ttlSeconds - 新的生存时间(秒)
   * @returns {Promise<boolean>} 操作是否成功
   */
  static async updateExpiry(id, ttlSeconds) {
    const pool = await getConnection();
    
    const newExpiresAt = new Date(Date.now() + ttlSeconds * 1000);
    
    const [result] = await pool.query(
      `UPDATE tts_cache SET expires_at = ? WHERE id = ?`,
      [newExpiresAt, id]
    );
    
    return result.affectedRows > 0;
  }

  /**
   * 为特定会话的所有缓存延长过期时间
   * @param {string} sessionId - 会话ID
   * @param {number} ttlSeconds - 新的生存时间(秒)
   * @returns {Promise<number>} 更新的记录数量
   */
  static async extendExpiryForSession(sessionId, ttlSeconds) {
    const pool = await getConnection();
    
    const newExpiresAt = new Date(Date.now() + ttlSeconds * 1000);
    
    const [result] = await pool.query(
      `UPDATE tts_cache SET expires_at = ? WHERE session_id = ?`,
      [newExpiresAt, sessionId]
    );
    
    return result.affectedRows;
  }
}

module.exports = TtsCache;