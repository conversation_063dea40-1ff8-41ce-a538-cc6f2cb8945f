const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { optionalAuthenticateToken } = require('../middleware/auth');
const os = require('os');
const TtsCache = require('../models/TtsCache');
const { getVoiceForReader, basicTtsVoices } = require('../config/tts_voices');

// 设置默认语音 (免费basic-tts版本)
const DEFAULT_VOICE = basicTtsVoices.default;

/**
 * @api {post} /api/tts 将文本转换为语音
 * @apiName BasicTextToSpeech
 * @apiGroup TTS
 * @apiDescription 使用免费的basic-tts(edge-tts)将文本转换为语音并返回音频数据
 * 
 * @apiParam {String} text 需要转换的文本
 * @apiParam {String} [sessionId] 关联的会话ID
 * @apiParam {String} [voice] 语音角色(当前版本会支持不同占卜师对应不同音色)
 * @apiParam {String} [readerId] 占卜师ID，用于选择对应的音色
 * @apiParam {String} [messageId] 消息ID
 * @apiParam {String} [cacheKey] 缓存键
 * @apiParam {String} [language] 语言代码，如'zh'、'en'、'ja'，默认为'zh'
 * 
 * @apiSuccess {Binary} audio 音频文件二进制数据
 */
router.post('/', optionalAuthenticateToken, async (req, res) => {
  try {
    const { text, sessionId, voice: requestVoice, readerId, messageId, cacheKey, language = 'zh' } = req.body;
    
    // 根据readerId和language确定使用的语音，如果未指定则使用请求中的voice参数或默认语音
    const voice = requestVoice || (readerId ? getVoiceForReader(readerId, 'basic', language) : 
      language === 'zh' ? DEFAULT_VOICE : basicTtsVoices[`default_${language}`] || DEFAULT_VOICE);
    
    // 获取用户ID(如果已认证)
    const userId = req.user ? req.user.userId : null;

    // 初始化缓存相关变量
    let cacheMessageId = null;
    let cacheSessionId = null;
    let sectionType = 'base'; // 默认板块类型

    if (!text || text.trim() === '') {
      return res.status(400).json({ error: '文本不能为空' });
    }

    // 将缓存键处理和查询逻辑简化为单一流程
    //  console.log(`[Basic-TTS] 尝试从数据库中查找缓存的音频...`);
    
    // 优先使用直接提供的sessionId和messageId
    if (sessionId) {
      cacheSessionId = sessionId;
      
      // 标准化messageId格式
      if (messageId) {
        if (messageId.includes('_para_')) {
          // 已经是标准格式
          cacheMessageId = messageId;
        } else {
          // 提取数字部分构建标准格式
          const numMatch = messageId.match(/(\d+)$/);
          const index = numMatch ? numMatch[1] : '0';
          cacheMessageId = `${sectionType}_para_${index}`;
        }
      } else {
        // 没有提供messageId，使用默认值
        cacheMessageId = `${sectionType}_para_0`;
      }
    }
    // 备用方案：从cacheKey提取信息（兼容旧版本客户端）
    else if (cacheKey) {
      // console.log(`[Basic-TTS] 未提供sessionId，尝试从缓存键解析: ${cacheKey}`);
      
      // 尝试从缓存键中提取会话ID和消息ID
      if (cacheKey.includes('_paragraph_')) {
        // 提取会话ID
        const parts = cacheKey.split('_');
        cacheSessionId = parts[0];
        
        // 提取段落索引
        let paragraphIndex = '0';
        const paraMatch = cacheKey.match(/_paragraph_(\d+)/);
        if (paraMatch) {
          paragraphIndex = paraMatch[1];
        }
        
        // 提取板块类型
        if (cacheKey.includes('_base_') || cacheKey.includes('/base/')) {
          sectionType = 'base';
        } else if (cacheKey.includes('_follow_') || cacheKey.includes('/follow/')) {
          sectionType = 'follow';
        }
        
        // 构建标准格式的消息ID
        cacheMessageId = `${sectionType}_para_${paragraphIndex}`;
        // console.log(`[Basic-TTS] 从段落格式提取: 会话ID=${cacheSessionId}, 板块=${sectionType}, 索引=${paragraphIndex}, 消息ID=${cacheMessageId}`);
      } 
      else if (cacheKey.includes('_para_')) {
        // 已经是标准格式的情况
        const parts = cacheKey.split('_');
        if (parts.length >= 4) {
          cacheSessionId = parts[0];
          sectionType = parts[1];
          const index = parts[parts.length - 1];
          cacheMessageId = `${sectionType}_para_${index}`;
          // console.log(`[Basic-TTS] 使用已有新格式: 会话ID=${cacheSessionId}, 消息ID=${cacheMessageId}`);
        }
      }
    }
    
    // 查询缓存
    let existingCache = null;
    if (cacheSessionId && cacheMessageId) {
      // console.log(`[Basic-TTS] 使用会话ID和消息ID查询缓存: sessionId=${cacheSessionId}, messageId=${cacheMessageId}`);
      existingCache = await TtsCache.findBySessionAndMessageId(cacheSessionId, cacheMessageId);
    } else {
      // console.log(`[Basic-TTS] 无法获取有效的会话ID和消息ID，无法查询缓存`);
    }
    
    if (existingCache) {
      // console.log(`[Basic-TTS] 缓存命中! ID=${existingCache.id}, 创建时间=${existingCache.created_at}, 过期时间=${existingCache.expires_at}`);
      res.set('Content-Type', 'audio/mpeg');
      res.set('Cache-Control', 'public, max-age=86400'); // 24小时客户端缓存
      return res.send(existingCache.audio_data);
    } else {
      // console.log(`[Basic-TTS] 缓存未命中，需要生成新的音频文件`);
    }
    
    // 没有找到缓存，需要生成新的音频
    // 创建临时目录用于存放临时文件
    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'basic-tts-'));
    
    // 生成临时文件路径
    const textFilePath = path.join(tempDir, 'text.txt');
    const outputPath = path.join(tempDir, 'output.mp3');
    
    // 将文本写入临时文件以避免命令行参数过长问题
    fs.writeFileSync(textFilePath, text, 'utf8');
    
    // 判断文本长度设置适当的缓存过期时间
    let ttl = 1800; // 默认30分钟缓存
    
    // 根据是否为段落和文本长度调整缓存时间
    if (messageId && messageId.includes('_para_')) {
      ttl = 86400 * 7; // 段落缓存7天
    } 
    // 根据文本长度调整普通缓存时间
    else {
      if (text.length > 500) {
        ttl = 3600 * 24; // 长文本24小时
      } else if (text.length > 100) {
        ttl = 3600 * 12; // 中等文本12小时
      } else {
        ttl = 3600 * 6;  // 短文本6小时
      }
    }
    
    // 记录缓存策略
    // console.log(`[Basic-TTS] 缓存策略: TTL=${ttl}秒, 文本长度=${text.length}`);
    
    // 调用Python脚本进行TTS转换
    // 支持通过环境变量PYTHON_EXEC_PATH指定Python解释器路径
    // 这样在不同环境下可以使用不同的Python路径，特别是在服务器上使用虚拟环境
    const pythonExec = process.platform === 'win32' 
      ? 'python' 
      : (process.env.PYTHON_EXEC_PATH || 'python3');
    const pythonScript = path.join(__dirname, '../basic-tts/basic_tts.py');
    
    // console.log(`[Basic-TTS] 调用Python脚本生成音频: ${pythonExec} ${pythonScript}`);
    
    const pythonProcess = spawn(pythonExec, [
      pythonScript,
      textFilePath, // 传递文本文件路径而不是文本内容
      voice,
      outputPath
    ]);

    let errorOutput = '';
    let stdOutput = '';
    
    pythonProcess.stdout.on('data', (data) => {
      stdOutput += data.toString();
    });
    
    pythonProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    pythonProcess.on('close', async (code) => {
      try {
        // 清理临时文本文件
        if (fs.existsSync(textFilePath)) {
          fs.unlinkSync(textFilePath);
        }
        
        if (code !== 0) {
          return res.status(500).json({
            error: '语音生成失败',
            details: errorOutput,
            stdout: stdOutput,
            code
          });
        }

        // 检查文件是否成功创建
        if (fs.existsSync(outputPath)) {
          // console.log(`[Basic-TTS] 音频文件生成成功: ${outputPath}`);
          // 读取生成的音频文件
          const audioData = fs.readFileSync(outputPath);
          // console.log(`[Basic-TTS] 音频文件大小: ${audioData.length} 字节`);
          
          // 保存到数据库前，确保有最终的会话ID和消息ID
          if (!cacheSessionId && sessionId) {
            cacheSessionId = sessionId;
          }
          
          if (!cacheMessageId && cacheSessionId) {
            // 创建默认的消息ID，如果没有
            let section = sectionType || 'base';  // 默认使用base板块
            let paraIndex = '0';  // 默认索引
            
            // 尝试从messageId提取索引
            if (messageId) {
              const numMatch = messageId.match(/(\d+)$/);
              if (numMatch) {
                paraIndex = numMatch[1];
              }
            }
            
            cacheMessageId = `${section}_para_${paraIndex}`;
            // console.log(`[Basic-TTS] 创建默认消息ID: ${cacheMessageId}`);
          }
          
          try {
            // 不再需要构建cacheQueryKey
            
            await TtsCache.create({
              text: text, 
              voice,
              audioData,
              ttl,
              sessionId: cacheSessionId,
              userId: userId || null,
              messageId: cacheMessageId || null
            });
            // console.log(`[Basic-TTS] 音频缓存已保存到数据库，TTL=${ttl}秒, sessionId=${cacheSessionId}, messageId=${cacheMessageId}`);
          } catch (cacheError) {
            // console.error(`[Basic-TTS] 缓存音频到数据库失败: ${cacheError.message}`);
          }
          
          // 清理临时输出文件
          fs.unlinkSync(outputPath);
          
          // 发送音频数据给客户端
          res.set('Content-Type', 'audio/mpeg');
          res.set('Cache-Control', 'public, max-age=86400'); // 24小时客户端缓存
          // console.log(`[Basic-TTS] 发送音频数据给客户端完成`);
          res.send(audioData);
        } else {
          res.status(500).json({
            error: '语音文件生成失败',
            details: '文件未创建',
            stdout: stdOutput,
            stderr: errorOutput
          });
        }
      } catch (err) {
        // 尝试清理临时文件
        try {
          if (fs.existsSync(outputPath)) {
            fs.unlinkSync(outputPath);
          }
          fs.rmdirSync(tempDir, { recursive: true });
        } catch (cleanupErr) {
          // console.error('[Basic-TTS] 清理临时文件失败:', cleanupErr);
        }

        res.status(500).json({
          error: '处理音频文件时出错',
          details: err.message
        });
      }
    });
  } catch (error) {
    res.status(500).json({ error: '服务器错误', details: error.message });
  }
});

/**
 * @api {get} /api/tts/audio/:id 通过ID获取特定的音频
 * @apiName GetTtsAudioById
 * @apiGroup TTS
 * @apiDescription 获取之前生成的TTS音频数据
 * 
 * @apiParam {String} id 音频缓存ID
 * 
 * @apiSuccess {Binary} audio 音频文件二进制数据
 */
router.get('/audio/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ error: '缺少音频ID参数' });
    }
    
    // 获取音频数据
    const audioData = await TtsCache.getAudioById(id);
    
    if (!audioData) {
      return res.status(404).json({ error: '未找到音频数据或已过期' });
    }
    
    // 返回音频数据
    res.set('Content-Type', 'audio/mpeg');
    res.set('Cache-Control', 'public, max-age=86400'); // 24小时客户端缓存
    res.send(audioData);
    
  } catch (error) {
    res.status(500).json({ error: '服务器错误', details: error.message });
  }
});

/**
 * @api {get} /api/tts/session/:sessionId 获取会话相关的所有TTS记录
 * @apiName GetTtsBySession
 * @apiGroup TTS
 * @apiDescription 获取特定会话关联的所有TTS记录(不含音频数据)
 * 
 * @apiParam {String} sessionId 会话ID
 * 
 * @apiSuccess {Array} ttsRecords TTS记录列表
 */
router.get('/session/:sessionId', optionalAuthenticateToken, async (req, res) => {
  try {
    const { sessionId } = req.params;
    
    if (!sessionId) {
      return res.status(400).json({ error: '缺少会话ID参数' });
    }
    
    // 获取会话相关的TTS记录
    // console.log(`[TTS] 查询会话ID为${sessionId}的所有TTS记录`);
    const records = await TtsCache.findBySessionId(sessionId);
    // console.log(`[TTS] 找到会话${sessionId}的记录数量: ${records.length}`);
    
    res.json({ records });
    
  } catch (error) {
    console.error(`[TTS] 获取会话相关TTS记录时出错:`, error);
    res.status(500).json({ error: '服务器错误', details: error.message });
  }
});

/**
 * @api {get} /api/tts/user 获取用户相关的所有TTS记录
 * @apiName GetTtsByUser
 * @apiGroup TTS
 * @apiDescription 获取当前登录用户关联的所有TTS记录(不含音频数据)
 * 
 * @apiParam {Number} [limit=100] 每页记录数量
 * @apiParam {Number} [offset=0] 偏移量
 * 
 * @apiSuccess {Array} ttsRecords TTS记录列表
 * @apiSuccess {Number} total 总记录数
 */
router.get('/user', optionalAuthenticateToken, async (req, res) => {
  try {
    // 检查用户是否已登录
    if (!req.user || !req.user.id) {
      return res.status(401).json({ error: '需要登录才能访问用户TTS记录' });
    }
    
    const limit = parseInt(req.query.limit) || 100;
    const offset = parseInt(req.query.offset) || 0;
    
    // 获取用户相关的TTS记录
    console.log(`[TTS] 查询用户ID为${req.user.id}的TTS记录，参数: limit=${limit}, offset=${offset}`);
    const result = await TtsCache.findByUserId(req.user.id, { limit, offset });
    console.log(`[TTS] 找到用户${req.user.id}的记录数量: ${result.data.length}/${result.total}`);
    
    res.json(result);
    
  } catch (error) {
    console.error(`[TTS] 获取用户相关TTS记录时出错:`, error);
    res.status(500).json({ error: '服务器错误', details: error.message });
  }
});

module.exports = router; 