const express = require('express');
const router = express.Router();
const { optionalAuthenticateToken } = require('../middleware/auth');
const basicTtsRouter = require('./basic-tts');
const proTtsRouter = require('./pro-tts');
const TtsCache = require('../models/TtsCache');
const { User } = require('../models/User');
const { basicTtsVoices, proTtsVoices, getVoiceForReader } = require('../config/tts_voices');

/**
 * @api {post} /api/tts/generate 生成语音
 * @apiName GenerateTTS
 * @apiGroup TTS
 * @apiDescription 将文本转换为语音
 * 
 * @apiParam {String} text 要转换的文本
 * @apiParam {String} [readerId] 占卜师ID(basic/molly/claire/raven等)
 * @apiParam {String} [language] 语言(zh-CN/en)，默认为zh-CN
 * @apiParam {String} [sessionId] 关联的会话ID
 * @apiParam {String} [voice] 语音角色(可选)
 * @apiParam {String} [messageId] 消息ID，用于段落级缓存
 * @apiParam {String} [cacheKey] 缓存键，用于高级缓存识别
 * 
 * @apiSuccess {Binary} audio 音频文件二进制数据
 */
router.post('/generate', optionalAuthenticateToken, async (req, res, next) => {
  try {
    const { readerId } = req.body;
    
    // 记录路由分发信息
    // console.log(`[TTS] 分发TTS请求: readerId=${readerId || 'undefined'}`);
    
    // 如果是茉伊(basic)占卜师，使用basic-tts
    if (!readerId || readerId === 'basic' || readerId === 'Molly') {
      // console.log(`[TTS] 为茉伊(basic)占卜师使用basic-tts，占卜师ID=${readerId || 'basic'}`);
      // 转发请求到basic-tts
      return basicTtsRouter.handle(req, res, next);
    }
    
    // 获取完整的用户信息
    let userVipStatus = 'none';
    let isVipExpired = false;
    
    if (req.user && req.user.userId) {
      try {
        const userInfo = await User.findById(req.user.userId);
        if (userInfo) {
          // 检查VIP是否过期
          if (userInfo.vip_status !== 'none' && userInfo.vip_end_date) {
            const now = new Date();
            const vipEndDate = new Date(userInfo.vip_end_date);
            
            if (now > vipEndDate) {
              // VIP已过期
              console.log('用户VIP已过期:', {
                userId: userInfo.id,
                vipEndDate: userInfo.vip_end_date,
                currentDate: now.toISOString()
              });
              userVipStatus = 'none';
              isVipExpired = true;
              
              // 更新用户VIP状态为none（异步操作，不影响当前请求）
              User.updateVipStatus(userInfo.id, 'none').catch(err => {
                console.error('更新过期VIP状态失败:', err);
              });
            } else {
              // VIP未过期
              userVipStatus = userInfo.vip_status;
              // console.log('用户VIP状态:', userVipStatus, '到期日:', userInfo.vip_end_date);
            }
          } else {
            // 用户没有VIP或没有设置过期日期
            userVipStatus = userInfo.vip_status;
            // console.log('用户VIP状态:', userVipStatus);
          }
        }
      } catch (error) {
        console.error('获取用户信息时出错:', error);
      }
    }

    // 其他占卜师需要检查VIP状态
    if (!req.user || userVipStatus !== 'active') {
      const errorMessage = isVipExpired ? 
        '您的VIP会员已过期，请续费后继续使用高级语音功能' : 
        '需要VIP会员才能使用高级语音功能';
      
      return res.status(403).json({ 
        error: errorMessage, 
        errorCode: isVipExpired ? 'VIP_EXPIRED' : 'VIP_REQUIRED',
        readerId: readerId
      });
    }
    
    // 用户是VIP，使用pro-tts
    // console.log(`[TTS] 为占卜师${readerId}使用pro-tts`);
    return proTtsRouter.handle(req, res, next);
  } catch (error) {
    console.error('TTS路由处理出错:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

/**
 * @api {post} /api/tts/check-block 检查板块中的所有音频文件是否存在
 * @apiName CheckBlockTTS
 * @apiGroup TTS
 * @apiDescription 检查指定会话和消息ID列表对应的音频文件是否都存在
 * 
 * @apiParam {String} sessionId 会话ID
 * @apiParam {Array} messageIds 消息ID数组
 * 
 * @apiSuccess {Boolean} success 是否成功
 * @apiSuccess {Boolean} allExist 所有音频文件是否都存在
 * @apiSuccess {Array} existingIds 存在的音频文件ID数组
 * @apiSuccess {Array} missingIds 不存在的音频文件ID数组
 */
router.post('/check-block', optionalAuthenticateToken, async (req, res) => {
  try {
    const { sessionId, messageIds } = req.body;
    const userId = req.user ? req.user.userId : 'anonymous';
    
    
    if (!sessionId || !messageIds || !Array.isArray(messageIds)) {
      return res.status(400).json({ 
        success: false, 
        message: '缺少必要参数' 
      });
    }

    // 检查每个消息ID对应的音频文件是否存在
    const existingIds = [];
    const missingIds = [];
    
    for (const messageId of messageIds) {
      const cache = await TtsCache.findBySessionAndMessageId(sessionId, messageId);
      if (cache) {
        existingIds.push(messageId);
      } else {
        missingIds.push(messageId);
      }
    }

    const allExist = missingIds.length === 0;
    return res.json({
      success: true,
      allExist,
      existingIds,
      missingIds
    });
  } catch (error) {
    return res.status(500).json({ 
      success: false, 
      message: '服务器错误' 
    });
  }
});

/**
 * @api {get} /api/tts/audio/:sessionId/:messageId 获取指定会话和消息ID的音频文件
 * @apiName GetAudioBySessionAndMessageId
 * @apiGroup TTS
 * @apiDescription 获取指定会话和消息ID的音频文件
 * 
 * @apiParam {String} sessionId 会话ID
 * @apiParam {String} messageId 消息ID
 * 
 * @apiSuccess {Binary} audio 音频文件二进制数据
 */
router.get('/audio/:sessionId/:messageId', optionalAuthenticateToken, async (req, res) => {
  try {
    const { sessionId, messageId } = req.params;
    const userId = req.user ? req.user.userId : 'anonymous';
    
    
    if (!sessionId || !messageId) {
      return res.status(400).json({ 
        success: false, 
        message: '缺少必要参数' 
      });
    }

    // 查询缓存
    const cache = await TtsCache.findBySessionAndMessageId(sessionId, messageId);
    
    if (!cache) {
      return res.status(404).json({ 
        success: false, 
        message: '音频文件不存在或已过期' 
      });
    }

    
    // 返回音频数据
    res.set('Content-Type', 'audio/mpeg');
    res.set('Cache-Control', 'public, max-age=86400'); // 24小时客户端缓存
    return res.send(cache.audio_data);
  } catch (error) {
    console.error('[TTS-Audio] 获取音频文件时出错:', error);
    return res.status(500).json({ 
      success: false, 
      message: '服务器错误' 
    });
  }
});

// 将其它TTS服务的GET请求直接透传
router.get('/audio/:id', (req, res, next) => {
  basicTtsRouter.handle(req, res, next);
});

/**
 * @api {get} /api/tts/session/:sessionId 获取会话相关的所有TTS记录
 * @apiName GetSessionTTS 
 * @apiGroup TTS
 * @apiDescription 获取会话相关的所有TTS记录
 * 
 * @apiParam {String} sessionId 会话ID
 * 
 * @apiSuccess {Array} records TTS记录数组
 */
router.get('/session/:sessionId', (req, res, next) => {
  basicTtsRouter.handle(req, res, next);
});

// 添加一个新的API端点，用于获取TTS音色配置
/**
 * @api {get} /api/tts/voices 获取TTS音色配置
 * @apiName GetTtsVoices
 * @apiGroup TTS
 * @apiDescription 获取所有TTS音色配置，包括基础TTS和高级TTS
 * 
 * @apiSuccess {Object} basicTtsVoices 基础TTS音色配置
 * @apiSuccess {Object} proTtsVoices 高级TTS音色配置
 */
router.get('/voices', (req, res) => {
  res.json({
    basicTtsVoices,
    proTtsVoices
  });
});

// 为了向后兼容，保留旧路径
router.post('/', optionalAuthenticateToken, async (req, res, next) => {
  try {
    const { readerId, sessionId, text } = req.body;

    // 如果是茉伊(basic)占卜师，使用basic-tts（允许未登录用户使用）
    if (!readerId || readerId === 'basic' || readerId === 'Molly') {
      // 转发请求到basic-tts
      return basicTtsRouter.handle(req, res, next);
    }
    
    // 特殊处理：如果是reader_intro会话ID，这是试听功能，允许所有用户访问
    if (sessionId === 'reader_intro') {
      return proTtsRouter.handle(req, res, next);
    }

    // 获取完整的用户信息
    let userVipStatus = 'none';
    let isVipExpired = false;

    if (req.user && req.user.userId) {
      try {
        const userInfo = await User.findById(req.user.userId);
        if (userInfo) {
          // 检查VIP是否过期
          if (userInfo.vip_status !== 'none' && userInfo.vip_end_date) {
            const now = new Date();
            const vipEndDate = new Date(userInfo.vip_end_date);

            if (now > vipEndDate) {
              // VIP已过期
              userVipStatus = 'none';
              isVipExpired = true;

              // 更新用户VIP状态为none（异步操作，不影响当前请求）
              User.updateVipStatus(userInfo.id, 'none').catch(err => {
                console.error('更新过期VIP状态失败:', err);
              });
            } else {
              // VIP未过期
              userVipStatus = userInfo.vip_status;
            }
          } else {
            // 用户没有VIP或没有设置过期日期
            userVipStatus = userInfo.vip_status;
          }
        }
      } catch (error) {
        console.error('获取用户信息时出错:', error);
        // 发生错误时，为了安全起见，将用户视为非VIP
        userVipStatus = 'none';
      }
    }
    
    // 其他占卜师需要检查VIP状态
    if (!req.user || userVipStatus !== 'active') {
      const errorMessage = isVipExpired ?
        '您的VIP会员已过期，请续费后继续使用高级语音功能' :
        '需要VIP会员才能使用高级语音功能';

      return res.status(403).json({
        error: errorMessage,
        errorCode: isVipExpired ? 'VIP_EXPIRED' : 'VIP_REQUIRED',
        readerId: readerId
      });
    }

    // 用户是VIP，使用pro-tts
    return proTtsRouter.handle(req, res, next);
  } catch (error) {
    console.error('TTS路由处理出错:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router; 