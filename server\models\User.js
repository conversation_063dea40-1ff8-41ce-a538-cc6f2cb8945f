const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');

class User {
  static async findByEmail(email) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      `SELECT 
        id, username, email, 
        is_email_verified, vip_status, remaining_reads, 
        vip_type, vip_end_date, auth_provider, avatar, 
        whether_paypal, country, birthday, user_profile, location,
        reset_code, reset_code_expiry, password, has_internal_privilege
      FROM users WHERE email = ?`,
      [email]
    );
    
    // 确保返回的用户对象的password字段是字符串类型
    if (rows[0] && rows[0].password !== null && typeof rows[0].password !== 'string') {
      console.log('转换password字段为字符串类型, 原类型:', typeof rows[0].password);
      // 如果是Buffer，转换为字符串
      if (Buffer.isBuffer(rows[0].password)) {
        rows[0].password = rows[0].password.toString();
      } else {
        // 其他类型尝试转为字符串
        rows[0].password = String(rows[0].password);
      }
    }
    
    return rows[0];
  }

  static async findByUsername(username) {
    const pool = await getConnection();
    const [rows] = await pool.query('SELECT * FROM users WHERE username = ?', [username]);
    
    // 确保返回的用户对象的password字段是字符串类型
    if (rows[0] && rows[0].password !== null && typeof rows[0].password !== 'string') {
      console.log('转换password字段为字符串类型, 原类型:', typeof rows[0].password);
      // 如果是Buffer，转换为字符串
      if (Buffer.isBuffer(rows[0].password)) {
        rows[0].password = rows[0].password.toString();
      } else {
        // 其他类型尝试转为字符串
        rows[0].password = String(rows[0].password);
      }
    }
    
    return rows[0];
  }

  static async findById(id) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      `SELECT 
        id, username, email, 
        is_email_verified, vip_status, remaining_reads, 
        vip_type, vip_end_date, auth_provider, avatar, 
        whether_paypal, country, birthday, user_profile, location,
        has_internal_privilege, browser_fingerprints
      FROM users WHERE id = ?`,
      [id]
    );
    
    if (rows[0]) {
      const user = rows[0];
      if (user.birthday) {
        user.birthday = user.birthday instanceof Date 
          ? user.birthday.toISOString().split('T')[0]
          : user.birthday;
      }
      return user;
    }
    return null;
  }

  static async create(userData) {
    const pool = await getConnection();
    const id = uuidv4();
    let hashedPassword = null;

    // 如果提供了密码，需要判断是否已经哈希过
    if (userData.password) {
      // 检查是否已经是哈希密码（bcrypt哈希通常以$2a$、$2b$或$2y$开头）
      if (typeof userData.password === 'string' && 
          (userData.password.startsWith('$2a$') || 
           userData.password.startsWith('$2b$') || 
           userData.password.startsWith('$2y$'))) {
        // console.log('密码已经是哈希值，直接使用');
        hashedPassword = userData.password;
      } else {
        console.log('对密码进行哈希处理');
        hashedPassword = await bcrypt.hash(userData.password, 10);
      }
    }

    // console.log('创建用户，指纹:', userData.fingerprint || '无');
    // console.log('创建用户时的remainingReads:', userData.remainingReads);
    
    // 存储指纹数据
    let fingerprintData = null;
    if (userData.fingerprint) {
      // 将单个指纹存储为JSON数组
      fingerprintData = JSON.stringify([userData.fingerprint]);
      // console.log('存储用户指纹数据:', fingerprintData);
    } else {
      // console.log('未提供指纹数据');
    }

    // 正确处理remainingReads，避免0值被默认值覆盖
    const remainingReads = userData.remainingReads !== undefined ? userData.remainingReads : 3;
    console.log('最终使用的remainingReads:', remainingReads);

    const [result] = await pool.query(
      `INSERT INTO users (
        id, username, email, password,
        is_email_verified, vip_status, remaining_reads,
        auth_provider, avatar, birthday, user_profile, location,
        country, ip, browser_fingerprints
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id,
        userData.username,
        userData.email,
        hashedPassword,
        userData.isEmailVerified || false,
        userData.vipStatus || 'none',
        remainingReads,
        userData.authProvider || 'email',
        userData.avatar || null,
        userData.birthday || null,
        userData.userProfile || null,
        userData.location || null,
        userData.country || null,
        userData.ip || null,
        fingerprintData
      ]
    );

    // console.log('用户创建结果:', {
    //   id,
    //   email: userData.email,
    //   remainingReads: remainingReads,
    //   hasFingerprint: userData.fingerprint ? 'true' : 'false',
    //   fingerprintSaved: fingerprintData ? 'true' : 'false',
    //   passwordSet: hashedPassword ? 'true' : 'false'
    // });

    return { id, ...userData };
  }

  static async comparePassword(plainPassword, hashedPassword) {
    // 添加类型检查，确保hashedPassword是字符串
    if (!hashedPassword || typeof hashedPassword !== 'string') {
      console.error('无效的hashedPassword类型:', typeof hashedPassword, '值:', 
        hashedPassword ? hashedPassword.substring(0, 10) + '...' : 'null/undefined');
      return false;
    }
    
    if (!plainPassword) {
      console.error('提供的明文密码为空');
      return false;
    }
    
    try {
      // console.log('比较密码:',{
      //   plainLength: plainPassword.length,
      //   hashedLength: hashedPassword.length,
      //   hashedPreview: hashedPassword.substring(0, 10) + '...'
      // });
      const result = await bcrypt.compare(plainPassword, hashedPassword);
      // console.log('密码比较结果:', result);
      return result;
    } catch (error) {
      // console.error('密码比较出错:', error);
      return false;
    }
  }

  static async updateVipStatus(userId, vipStatus) {
    const pool = await getConnection();
    const [result] = await pool.query(
      'UPDATE users SET vip_status = ? WHERE id = ?',
      [vipStatus, userId]
    );
    return result.affectedRows > 0;
  }

  static async updateRemainingReads(userId, remainingReads) {
    const pool = await getConnection();
    const [result] = await pool.query(
      'UPDATE users SET remaining_reads = ? WHERE id = ?',
      [remainingReads, userId]
    );
    return result.affectedRows > 0;
  }

  static async verifyEmail(userId) {
    const pool = await getConnection();
    const [result] = await pool.query(
      'UPDATE users SET is_email_verified = true WHERE id = ?',
      [userId]
    );
    return result.affectedRows > 0;
  }

  static async updateResetToken(userId, resetToken) {
    const pool = await getConnection();
    const [result] = await pool.query(
      'UPDATE users SET reset_token = ? WHERE id = ?',
      [resetToken, userId]
    );
    return result.affectedRows > 0;
  }

  static async updatePassword(userId, newPassword) {
    const pool = await getConnection();
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    const [result] = await pool.query(
      'UPDATE users SET password = ?, reset_code = NULL, reset_code_expiry = NULL WHERE id = ?',
      [hashedPassword, userId]
    );
    return result.affectedRows > 0;
  }

  static async update(userId, data) {
    const pool = await getConnection();
    const updateFields = [];
    const values = [];

    // 使用显式的字段映射，而不是自动转换
    const fieldMap = {
      resetCode: 'reset_code',
      resetCodeExpiry: 'reset_code_expiry',
      // 添加其他字段的映射...
      isEmailVerified: 'is_email_verified',
      vipStatus: 'vip_status',
      remainingReads: 'remaining_reads',
      vipType: 'vip_type',
      vipEndDate: 'vip_end_date',
      authProvider: 'auth_provider',
      whetherPaypal: 'whether_paypal',
      userProfile: 'user_profile'
    };

    Object.entries(data).forEach(([key, value]) => {
      const fieldName = fieldMap[key] || key.toLowerCase();

      // 特殊处理生日字段，验证日期格式
      if (key === 'birthday' && value) {
        // 验证日期格式是否为 YYYY-MM-DD 且是有效日期
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(value)) {
          console.log('Invalid birthday format:', value);
          return; // 跳过无效的生日格式
        }

        // 验证日期是否有效
        const date = new Date(value);
        if (isNaN(date.getTime()) || date.toISOString().split('T')[0] !== value) {
          console.log('Invalid birthday date:', value);
          return; // 跳过无效的日期
        }
      }

      updateFields.push(`${fieldName} = ?`);
      values.push(value);
    });
    
    values.push(userId);
    
    const [result] = await pool.query(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      values
    );

    // 获取更新后的用户数据
    if (result.affectedRows > 0) {
      const [updatedUser] = await pool.query(
        `SELECT 
          id, username, email, 
          is_email_verified, vip_status, remaining_reads, 
          vip_type, vip_end_date, auth_provider, avatar, 
          whether_paypal, country, birthday, user_profile, location,
          reset_code, reset_code_expiry
        FROM users WHERE id = ?`,
        [userId]
      );
      
      if (updatedUser[0]) {
        const user = updatedUser[0];
        if (user.birthday) {
          user.birthday = user.birthday instanceof Date 
            ? user.birthday.toISOString().split('T')[0]
            : user.birthday;
        }
        return user;
      }
    }
    return null;
  }

  static async hasInternalPrivilege(userId) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      'SELECT has_internal_privilege FROM users WHERE id = ?',
      [userId]
    );
    return rows[0] && rows[0].has_internal_privilege;
  }

  static async hasUsedDiscount(userId) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      'SELECT has_used_discount FROM users WHERE id = ?',
      [userId]
    );
    return rows[0] && rows[0].has_used_discount;
  }

  static async markDiscountAsUsed(userId) {
    const pool = await getConnection();
    const [result] = await pool.query(
      'UPDATE users SET has_used_discount = true WHERE id = ?',
      [userId]
    );
    return result.affectedRows > 0;
  }

  static async getInvitationCodeInfo(userId) {
    const pool = await getConnection();
    const [rows] = await pool.query(
      `SELECT 
        u.has_internal_privilege, u.privilege_granted_at,
        u.invitation_code as code,
        ic.created_at as code_created_at,
        sp.name as sales_person_name
      FROM users u
      LEFT JOIN invitation_codes ic ON u.invitation_code = ic.code
      LEFT JOIN sales_persons sp ON ic.sales_id = sp.id
      WHERE u.id = ?`,
      [userId]
    );
    return rows[0];
  }

  static async findByFingerprint(fingerprint) {
    if (!fingerprint) {
      console.log('查询指纹: 未提供指纹');
      return [];
    }
    
    const pool = await getConnection();
    try {
      console.log('查询指纹:', fingerprint);
      
      // 由于fingerprints存储为JSON数组，我们需要使用JSON_CONTAINS函数来检查
      // 注意：这需要MySQL 5.7+或MariaDB 10.2.3+的支持
      const [rows] = await pool.query(
        `SELECT id, username, email, remaining_reads, browser_fingerprints 
         FROM users 
         WHERE JSON_CONTAINS(browser_fingerprints, ?)`,
        [JSON.stringify(fingerprint)]
      );
      
      console.log(`查询指纹结果: 找到${rows.length}个匹配的用户`);
      return rows;
    } catch (error) {
      // 如果JSON_CONTAINS失败(可能数据库不支持或browser_fingerprints不是有效JSON)
      // 回退到模糊匹配，但使用更严格的匹配条件，避免过多的误判
      console.error('JSON查询指纹失败，尝试备用方法:', error);
      try {
        // 对于新格式的指纹 (使用 '-' 分隔)，我们需要特殊处理
        const isNewFormat = fingerprint.includes('-');
        const searchPattern = isNewFormat ? 
          // 对于新格式，我们取第一部分(visitorId)作为搜索关键字，它是最重要的识别部分
          `%${fingerprint.split('-')[0]}%` : 
          // 对于旧格式，仍然使用完整指纹
          `%${fingerprint}%`;
          
        console.log('使用模式搜索指纹:', searchPattern);
        
        const [fallbackRows] = await pool.query(
          `SELECT id, username, email, remaining_reads, browser_fingerprints 
           FROM users 
           WHERE browser_fingerprints LIKE ?`,
          [searchPattern]
        );
        
        // 过滤结果，只保留真正包含指纹的记录
        const filteredRows = fallbackRows.filter(row => {
          try {
            // 尝试解析JSON
            const fingerprints = JSON.parse(row.browser_fingerprints || '[]');
            
            // 检查是否为数组
            if (!Array.isArray(fingerprints)) {
              return row.browser_fingerprints === fingerprint;
            }
            
            // 对于新格式的指纹，我们需要特殊检查
            if (isNewFormat) {
              const visitorId = fingerprint.split('-')[0];
              // 检查是否有任何指纹包含相同的visitorId部分
              return fingerprints.some(fp => {
                if (fp.includes('-')) {
                  // 如果是新格式，比较visitorId部分
                  return fp.split('-')[0] === visitorId;
                } else if (fp.includes('|')) {
                  // 如果是旧格式，比较第一部分(也是visitorId)
                  return fp.split('|')[0] === visitorId;
                }
                // 完全不匹配格式则完整比较
                return fp === fingerprint;
              });
            } else {
              // 对于旧格式，保持原来的完整匹配检查
              return fingerprints.includes(fingerprint);
            }
          } catch (e) {
            // 如果不是JSON，直接检查是否完全匹配
            return row.browser_fingerprints === fingerprint;
          }
        });
        
        console.log(`备用查询指纹结果: 找到${filteredRows.length}个匹配的用户`);
        return filteredRows;
      } catch (fallbackError) {
        console.error('备用查询指纹也失败:', fallbackError);
        return [];
      }
    }
  }

  static async updateFingerprint(userId, fingerprint) {
    if (!userId || !fingerprint) {
      console.log('更新指纹: 缺少用户ID或指纹');
      return false;
    }
    
    const pool = await getConnection();
    try {
      console.log('更新用户指纹:', { userId, fingerprint });
      // 先获取用户现有的指纹数据
      const [rows] = await pool.query(
        'SELECT browser_fingerprints FROM users WHERE id = ?',
        [userId]
      );
      
      if (!rows || rows.length === 0) {
        console.log('找不到用户，无法更新指纹:', userId);
        return false;
      }
      
      let fingerprints = [];
      
      // 如果已有指纹数据，解析它
      if (rows[0].browser_fingerprints) {
        try {
          // console.log('现有指纹数据:', rows[0].browser_fingerprints);
          fingerprints = JSON.parse(rows[0].browser_fingerprints);
          // 确保它是一个数组
          if (!Array.isArray(fingerprints)) {
            // console.log('现有指纹不是数组，转换为数组');
            fingerprints = [rows[0].browser_fingerprints];
          }
        } catch (e) {
          // 如果现有数据不是JSON格式，将其作为单个元素
          // console.error('解析现有指纹失败:', e);
          fingerprints = [rows[0].browser_fingerprints];
        }
      } else {
        // console.log('用户没有现有指纹');
      }
      
      // 检查新指纹是否已存在
      if (!fingerprints.includes(fingerprint)) {
        // console.log('添加新指纹到用户记录');
        fingerprints.push(fingerprint);
      } else {
        // console.log('指纹已存在，无需添加');
      }
      
      const fingerprintJson = JSON.stringify(fingerprints);
      // console.log('更新后的指纹数据:', fingerprintJson);
      
      // 更新用户的指纹数据
      const [result] = await pool.query(
        'UPDATE users SET browser_fingerprints = ? WHERE id = ?',
        [fingerprintJson, userId]
      );
      
      // console.log('指纹更新结果:', { 
      //   success: result.affectedRows > 0, 
      //   affectedRows: result.affectedRows 
      // });
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新指纹失败:', error);
      return false;
    }
  }
}

module.exports = { User };