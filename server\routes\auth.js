const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const nodemailer = require('nodemailer');
const { User } = require('../models/User');
const { TempUser } = require('../models/TempUser');
const { authenticateToken } = require('../middleware/auth');
const { jwtDecode } = require('jwt-decode');
const { getConnection } = require('../services/database');
const geoip = require('geoip-lite');
const { translate } = require('../i18n');
const EmailService = require('../services/emailService');

// 注释掉原来的transporter配置，使用EmailService代替
/*
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  },
  // 163邮箱的优化配置
  connectionTimeout: 30000,    // 连接超时时间30秒
  greetingTimeout: 15000,     // 问候超时时间15秒
  socketTimeout: 30000,       // 套接字超时时间30秒
  debug: process.env.NODE_ENV === 'development', // 仅在开发环境启用调试
  tls: {
    rejectUnauthorized: true  // 验证SSL证书
  }
});

// 测试邮件配置
transporter.verify(function(error, success) {
  if (error) {
    console.error('邮件配置错误:', error);
  } else {
    console.log('邮件服务器连接成功，准备发送邮件');
  }
});
*/

/* 
// OAuth2认证配置示例（更安全的方法）
// 需要先在Google Cloud Console创建OAuth客户端ID和密钥
// const transporter = nodemailer.createTransport({
//   host: 'smtp.gmail.com',
//   port: 465,
//   secure: true,
//   auth: {
//     type: 'OAuth2',
//     user: process.env.EMAIL_USER,
//     clientId: process.env.GMAIL_CLIENT_ID,
//     clientSecret: process.env.GMAIL_CLIENT_SECRET,
//     refreshToken: process.env.GMAIL_REFRESH_TOKEN,
//     accessToken: process.env.GMAIL_ACCESS_TOKEN
//   }
// });
*/

// Generate verification code
const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 邮件发送函数，使用EmailService，不包含重试逻辑
async function sendMailWithRetry(mailOptions, maxRetries = 3) {
  // 保留 maxRetries 参数以保持接口兼容性，但不使用它
  return await EmailService.sendMailWithRetry(mailOptions);
}

// 注册路由
router.post('/register', async (req, res) => {
  try {
    const { email, password, username, language, fingerprint } = req.body;
    
    console.log('收到注册请求:', { 
      email, 
      username, 
      language, 
      fingerprint: fingerprint ? '已提供' : '未提供' 
    });
    
    // if (fingerprint) {
    //   console.log('注册请求中的指纹详情:', fingerprint);
    // }
    
    // 验证邮箱格式
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    if (!emailRegex.test(email)) {
      console.log('邮箱格式验证失败:', email);
      return res.status(400).json({ message: req.t('auth.validation.invalidEmail') });
    }

    // 验证密码格式 - 只允许字母、数字、下划线
    const passwordRegex = /^[a-zA-Z0-9_]+$/;
    if (!passwordRegex.test(password)) {
      console.log('密码格式验证失败: 密码只能包含字母、数字和下划线');
      return res.status(400).json({ message: req.t('auth.validation.invalidPassword') || '密码只能包含字母、数字和下划线' });
    }

    // 检查邮箱是否已被注册
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      console.log('邮箱已被注册:', email);
      return res.status(400).json({ message: req.t('auth.validation.emailExists') });
    }

    // 如果提供了指纹，检查是否有其他账号使用相同指纹
    let shouldResetRemainingReads = false;
    
    if (fingerprint) {
      // console.log('开始查找是否有相同指纹的账号:', fingerprint);
      const existingUsersWithFingerprint = await User.findByFingerprint(fingerprint);
      
      if (existingUsersWithFingerprint && existingUsersWithFingerprint.length > 0) {
        console.log(`发现${existingUsersWithFingerprint.length}个账号使用相同指纹，新用户将被设置为0次阅读次数:`, 
          existingUsersWithFingerprint.map(u => ({ id: u.id, email: u.email })));
        shouldResetRemainingReads = true;
      } else {
        console.log('未找到使用相同指纹的账号');
      }
    } else {
      console.log('注册请求未提供指纹信息');
    }
    
    // 生成验证码
    const verificationCode = generateVerificationCode();
    // console.log('生成的验证码:', verificationCode);
    
    // 将注册信息存储在临时表中
    // console.log('创建临时用户，包含指纹:', fingerprint || '无');
    const tempUser = await TempUser.create({
      email,
      password,
      username,
      verificationCode, // 确保这里的字段名与TempUser.create中的参数名一致
      language: language || req.language, // 保存用户的语言偏好
      fingerprint
    });
    
    // 创建后检查临时用户信息
    // console.log('临时用户创建完成:', { 
    //   userId: tempUser.id, 
    //   email,
    //   hasFingerprint: tempUser.fingerprint ? 'true' : 'false',
    //   fingerprint: tempUser.fingerprint
    // });

    // 使用用户指定的语言或从请求中获取的语言
    const userLanguage = language || req.language;
    const { translate } = require('../i18n');
    
    // 创建特定语言的翻译函数
    const t = (key, params = {}) => translate(key, userLanguage, params);

    // 发送验证邮件
    try {
      // console.log('开始验证邮件服务器连接...');
      // await transporter.verify();
      // console.log('邮件服务器连接验证成功，开始发送邮件...');
      
      const mailOptions = {
        // 移除from字段，让EmailService自动设置
        to: email,
        subject: t('auth.email.registerSubject'),
        html: `
          <h1>${t('auth.email.registerTitle')}</h1>
          <p>${t('auth.email.registerCodeMessage')} <strong>${verificationCode}</strong></p>
          <p>${t('auth.email.registerCodeExpiry')}</p>
          <p>${t('auth.email.footer')}</p>
        `
      };
      
      // console.log('准备发送邮件，配置:', {
      //   from: mailOptions.from,
      //   to: mailOptions.to,
      //   subject: mailOptions.subject
      // });
      
      const info = await sendMailWithRetry(mailOptions);
      // console.log('邮件发送成功, messageId:', info.messageId);
      
      // 注册成功后，检查临时用户是否成功创建
      const checkTempUser = await TempUser.findById(tempUser.id);
      if (checkTempUser) {
        // console.log('临时用户验证成功:', {
        //   id: checkTempUser.id,
        //   email: checkTempUser.email,
        //   verification_code: checkTempUser.verification_code,
        //   verification_code_expiry: checkTempUser.verification_code_expiry
        // });
      } else {
        console.error('临时用户创建后未找到:', tempUser.id);
      }
      
      return res.status(200).json({
        message: req.t('auth.success.codeSent'),
        userId: tempUser.id,
        requireVerification: true
      });
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      console.error('错误详情:', {
        code: emailError.code,
        command: emailError.command,
        responseCode: emailError.responseCode,
        response: emailError.response
      });
      
      if (emailError.responseCode === 550) {
        return res.status(400).json({ message: req.t('auth.validation.emailNotReceivable') });
      }
      return res.status(500).json({ 
        message: req.t('auth.error.sendCode'),
        error: process.env.NODE_ENV === 'development' ? emailError.message : undefined
      });
    }
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: req.t('auth.error.registration') });
  }
});

// 验证邮箱路由
router.post('/verify-email', async (req, res) => {
  try {
    const { userId, verificationCode } = req.body;
    // console.log('收到验证邮箱请求:', { userId, verificationCode });

    const tempUser = await TempUser.findById(userId);
    if (!tempUser) {
      console.log('临时用户不存在:', userId);
      return res.status(400).json({ 
        success: false, 
        message: req.t('auth.registration.expiredInfo'),
        code: 'INFO_EXPIRED'
      });
    }
    
    // console.log('找到临时用户:', {
    //   id: tempUser.id,
    //   email: tempUser.email,
    //   username: tempUser.username,
    //   fingerprint: tempUser.fingerprint || '无',
    //   verification_code: tempUser.verification_code,
    //   verification_code_expiry: tempUser.verification_code_expiry,
    //   password: tempUser.password ? '已设置' : '未设置',
    //   passwordLength: tempUser.password ? tempUser.password.length : 0
    // });

    if (!tempUser.verification_code || !tempUser.verification_code_expiry) {
      console.log('验证码或过期时间不存在，删除临时用户:', userId);
      await TempUser.delete(userId);
      return res.status(400).json({ 
        success: false, 
        message: req.t('auth.registration.expiredCode'),
        code: 'CODE_EXPIRED'
      });
    }

    if (new Date() > new Date(tempUser.verification_code_expiry)) {
      console.log('验证码已过期，删除临时用户:', {
        userId,
        expiry: tempUser.verification_code_expiry,
        now: new Date()
      });
      await TempUser.delete(userId);
      return res.status(400).json({ 
        success: false, 
        message: req.t('auth.registration.expiredVerification'),
        code: 'CODE_EXPIRED'
      });
    }

    if (tempUser.verification_code !== verificationCode) {
      console.log('验证码不匹配:', {
        expected: tempUser.verification_code,
        received: verificationCode
      });
      return res.status(400).json({ 
        success: false, 
        message: req.t('auth.registration.incorrectCode'),
        code: 'INVALID_CODE'
      });
    }

    // 获取用户IP和地理位置信息
    const ip = req.headers['x-forwarded-for'] || 
               req.connection.remoteAddress || 
               req.socket.remoteAddress;
    const geo = geoip.lookup(ip);
    // console.log('用户IP和地理位置:', { ip, geo: geo ? geo.country : null });

    // 如果有指纹，检查是否需要重置阅读次数
    let remainingReads = 3; // 默认次数
    let fingerprintMatchedUsers = [];

    if (tempUser.fingerprint) {
      // console.log('验证邮箱阶段检查指纹:', tempUser.fingerprint);
      fingerprintMatchedUsers = await User.findByFingerprint(tempUser.fingerprint);

      if (fingerprintMatchedUsers && fingerprintMatchedUsers.length > 0) {
        console.log(`发现${fingerprintMatchedUsers.length}个账号使用相同指纹，新用户将被设置为0次阅读次数:`,
          fingerprintMatchedUsers.map(u => ({ id: u.id, email: u.email })));
        remainingReads = 0;
      } else {
        // 检查该指纹是否有匿名占卜记录
        try {
          const { getConnection } = require('../services/database');
          const pool = await getConnection();
          const [anonymousRecords] = await pool.query(
            'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE browser_fingerprint = ?',
            [tempUser.fingerprint]
          );

          const hasAnonymousRecord = anonymousRecords[0].count > 0;
          if (hasAnonymousRecord) {
            // 如果有匿名记录，则只赠送2次（总共3次减去已使用的1次）
            remainingReads = 2;
            console.log('用户指纹有匿名占卜记录，调整免费次数为2次');
          } else {
            console.log('验证阶段未找到使用相同指纹的账号，也无匿名占卜记录');
          }
        } catch (dbError) {
          console.error('检查匿名占卜记录失败:', dbError);
          // 出错时保持默认的3次
          console.log('数据库查询出错，保持默认3次免费占卜');
        }
      }
    } else {
      console.log('临时用户缺少指纹信息');
    }
    
    // 验证成功，创建正式用户
    console.log('验证成功，创建正式用户:', {
      email: tempUser.email,
      username: tempUser.username,
      fingerprint: tempUser.fingerprint || '无',
      remainingReads
    });
    
    // 确保使用bcryptjs重新哈希密码
    let hashedPassword = null;
    try {
      const bcrypt = require('bcryptjs');
      // 处理密码 - 我们直接使用tempUser.password，因为已经被哈希过了
      // console.log('临时用户密码类型:', typeof tempUser.password);
      hashedPassword = tempUser.password;
      // console.log('密码哈希完成:', hashedPassword ? hashedPassword.substring(0, 10) + '...' : '无法哈希');
    } catch (hashError) {
      console.error('密码哈希出错:', hashError);
      return res.status(500).json({ 
        success: false, 
        message: req.t('auth.error.verification')
      });
    }
    
    let newUser;
    try {
      newUser = await User.create({
        email: tempUser.email,
        password: hashedPassword, // 使用临时表中已哈希的密码
        username: tempUser.username,
        isEmailVerified: true,
        remainingReads: remainingReads,
        ip: ip || null,
        country: geo ? geo.country : null,
        fingerprint: tempUser.fingerprint
      });
    } catch (createError) {
      // 如果是重复邮箱错误，说明在验证过程中另一个请求已经创建了该用户
      if (createError.code === 'ER_DUP_ENTRY' && createError.sqlMessage.includes('email')) {
        console.log('检测到重复邮箱错误，用户可能已经被创建:', tempUser.email);
        // 查找已存在的用户
        newUser = await User.findByEmail(tempUser.email);
        if (!newUser) {
          console.error('重新查找用户失败，这不应该发生');
          return res.status(500).json({
            success: false,
            message: req.t('auth.error.verification')
          });
        }
        console.log('找到已存在的用户，继续登录流程:', newUser.id);
      } else {
        // 其他错误直接抛出
        console.error('创建用户时发生未知错误:', createError);
        return res.status(500).json({
          success: false,
          message: req.t('auth.error.verification')
        });
      }
    }
    
    // 创建后检查用户指纹是否被保存
    const createdUser = await User.findById(newUser.id);
    console.log('正式用户创建结果:', { 
      id: newUser.id, 
      email: newUser.email,
      remainingReads: remainingReads,
      fingerprint: createdUser ? (createdUser.browser_fingerprints || '无') : '无法获取',
      hasPassword: !!createdUser?.password,
      passwordType: createdUser?.password ? typeof createdUser.password : 'unknown'
    });

    // 注意：保留临时用户记录，不做删除操作
    // console.log('保留临时用户记录:', userId);

    // 生成 JWT token
    const token = jwt.sign(
      { userId: newUser.id, email: newUser.email },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    // 返回用户信息和 token
    // console.log('注册和验证完成，返回用户信息');
    res.json({
      success: true,
      message: req.t('auth.success.emailVerified'),
      user: {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        remainingReads: remainingReads,
        vipStatus: 'none',
        isEmailVerified: true,
        country: geo ? geo.country : null,
        ip: ip || null
      },
      token
    });
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({ success: false, message: req.t('auth.error.verification') });
  }
});

// 登录路由
router.post('/login', async (req, res) => {
  try {
    const { email, password, language, fingerprint } = req.body;
    console.log('登录请求:', { 
      email, 
      language, 
      hasPassword: !!password, 
      hasFingerprint: !!fingerprint,
      fingerprint: fingerprint ? fingerprint.substring(0, 10) + '...' : 'none'
    });

    // 使用用户指定的语言或从请求中获取的语言
    const userLanguage = language || req.language;
    const { translate } = require('../i18n');
    
    // 创建特定语言的翻译函数
    const t = (key, params = {}) => translate(key, userLanguage, params);

    // 查找用户
    try {
      const user = await User.findByEmail(email);
      // console.log('查找用户结果:', user ? { 
      //   id: user.id,
      //   email: user.email,
      //   hasPassword: !!user.password,
      //   hasFingerprint: !!user.browser_fingerprints
      // } : '未找到用户');
      
      if (!user) {
        console.log('用户不存在:', email);
        return res.status(400).json({ message: t('auth.validation.emailNotExists') });
      }

      // 验证密码
      // console.log('开始验证密码...');
      try {
        const isMatch = await User.comparePassword(password, user.password);
        console.log('密码验证结果:', isMatch);
        
        if (!isMatch) {
          console.log('密码不匹配');
          return res.status(400).json({ message: t('auth.validation.invalidCredentials') });
        }
      } catch (passwordError) {
        console.error('密码验证过程中出错:', passwordError);
        return res.status(400).json({ message: t('auth.validation.invalidCredentials') });
      }

      // 如果提供了指纹，更新用户的指纹信息
      if (fingerprint) {
        // console.log('更新用户指纹:', fingerprint.substring(0, 10) + '...');
        await User.updateFingerprint(user.id, fingerprint);
      }

      // 获取用户IP和地理位置信息
      const ip = req.headers['x-forwarded-for'] || 
                req.connection.remoteAddress || 
                req.socket.remoteAddress;
      const geo = geoip.lookup(ip);
      
      console.log('用户IP和地理信息:', { 
        ip, 
        country: geo ? geo.country : 'unknown' 
      });
      
      // 如果获取到地理位置信息，更新用户的country字段
      try {
        if (geo && geo.country) {
          console.log('更新用户国家和IP信息:', {
            userId: user.id,
            oldCountry: user.country || 'None',
            newCountry: geo.country
          });
          
          await User.update(user.id, { 
            country: geo.country,
            ip: ip || null
          });
          
          user.country = geo.country;
          user.ip = ip || null;
        } else if (ip) {
          // 即使没有获取到地理位置信息，也更新IP
          await User.update(user.id, { ip: ip });
          user.ip = ip;
        }
      } catch (geoUpdateError) {
        // 地理信息更新失败不应影响登录流程
        console.error('更新地理信息失败:', geoUpdateError);
      }
      
      // 生成 JWT token
      const token = jwt.sign(
        { userId: user.id, email: user.email },
        process.env.JWT_SECRET,
        { expiresIn: '7d' }
      );

      // 返回用户信息和 token
      const response = {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          gender: user.gender,
          age: user.age,
          remainingReads: user.remaining_reads,
          vipStatus: user.vip_status,
          vipType: user.vip_type,
          vipEndDate: user.vip_end_date,
          isEmailVerified: user.is_email_verified,
          whetherPaypal: user.whether_paypal,
          country: user.country,
          ip: user.ip
        },
        token
      };
      
      console.log('登录成功，返回用户信息:', { 
        id: response.user.id,
        username: response.user.username,
        email: response.user.email
      });
      
      res.json(response);
    } catch (userLookupError) {
      console.error('查找用户时出错:', userLookupError);
      return res.status(500).json({ message: t('auth.error.login') });
    }
  } catch (error) {
    console.error('登录过程中出错:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({ message: req.t('auth.error.login') });
  }
});

// 获取当前用户信息
router.get('/me', authenticateToken, async (req, res) => {
  try {
    // console.log('=== /me endpoint start ===');
    // console.log('User ID from token:', req.user.userId);
    
    const user = await User.findById(req.user.userId);
    if (!user) {
      console.log('User not found');
      return res.status(404).json({ message: req.t('auth.error.userNotFound') });
    }
    
    // console.log('User VIP info:', {
    //   vip_status: user.vip_status,
    //   vip_type: user.vip_type,
    //   vip_end_date: user.vip_end_date,
    //   whether_paypal: user.whether_paypal
    // });

    // 检查VIP是否过期
    if (user.vip_status !== 'none' && user.vip_end_date && new Date() > new Date(user.vip_end_date)) {
      console.log('VIP expired, updating status');
      await User.updateVipStatus(user.id, 'none');
      user.vip_status = 'none';
      user.vip_type = 'none';
      user.vip_end_date = null;
      console.log('Updated VIP info:', {
        vip_status: 'none',
        vip_type: 'none',
        vip_end_date: null,
        whether_paypal: user.whether_paypal
      });
    }

    // 获取用户所有占卜次数，排除每日运势（daily-fortune）的会话
    const pool = await getConnection();
    const [allSessions] = await pool.query(
      'SELECT COUNT(*) as count FROM sessions WHERE user_id = ? AND spread_id != "daily-fortune"',
      [user.id]
    );
    const sessionCount = allSessions[0].count;
    // console.log('Session count (excluding daily fortune):', sessionCount);
    // console.log('User remaining_reads from DB:', user.remaining_reads);

    // 计算实际剩余次数
    // VIP 用户显示无限次数，非 VIP 用户才计算实际剩余次数
    const actualRemainingReads = user.vip_status !== 'none' 
      ? Infinity 
      : Math.max(0, user.remaining_reads - sessionCount);
    // console.log('Calculated actualRemainingReads:', actualRemainingReads);

    // 转换为前端期望的格式
    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      gender: user.gender,
      age: user.age,
      remainingReads: actualRemainingReads,
      vipStatus: user.vip_status,
      vipType: user.vip_type,
      vipEndDate: user.vip_end_date,
      isEmailVerified: user.is_email_verified === 1,
      userProfile: user.user_profile,
      whether_paypal: user.whether_paypal,
      country: user.country,
      birthday: user.birthday,
      location: user.location,
      ip: user.ip
    };
    
    // console.log('Sending user response:', {
    //   ...userResponse,
    //   email: '[REDACTED]'  // 日志中隐藏敏感信息
    // });
    // console.log('=== /me endpoint end ===');

    res.json(userResponse);
  } catch (error) {
    console.error('Error in /me endpoint:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({ message: req.t('auth.error.getUserInfo') });
  }
});

// 发送重置密码邮件
router.post('/forgot-password', async (req, res) => {
  try {
    const { email, language } = req.body;

    // 查找用户
    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(400).json({ message: req.t('auth.validation.emailNotRegistered') });
    }

    // 生成重置密码的token
    const resetToken = jwt.sign(
      { userId: user.id },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    // 将重置token存储到数据库
    await User.updateResetToken(user.id, resetToken);

    // 使用用户指定的语言、用户存储的语言偏好或从请求中获取的语言
    const userLanguage = language || user.language || req.language;
    
    // 创建特定语言的翻译函数
    const t = (key, params = {}) => translate(key, userLanguage, params);

    // 发送重置密码邮件
    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    await EmailService.sendMailWithRetry({
      to: email,
      subject: t('auth.email.resetPasswordSubject'),
      html: `
        <h1>${t('auth.email.resetPasswordTitle')}</h1>
        <p>${t('auth.email.resetPasswordGreeting')}</p>
        <p>${t('auth.email.resetPasswordInstruction')}</p>
        <a href="${resetLink}">${resetLink}</a>
        <p>${t('auth.email.resetPasswordExpiry')}</p>
        <p>${t('auth.email.footer')}</p>
      `
    });

    res.json({ message: req.t('auth.success.resetLinkSent') });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({ message: req.t('auth.error.sendResetEmail') });
  }
});

// 发送重置密码验证码
router.post('/send-reset-code', async (req, res) => {
  try {
    const { email, language } = req.body;
    
    // 检查用户是否存在
    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(404).json({ message: req.t('auth.validation.emailNotRegistered') });
    }

    // 生成验证码
    const resetCode = generateVerificationCode();

    // 保存验证码和过期时间到用户记录
    const expiryTime = new Date(Date.now() + 30 * 60 * 1000);
    
    // 获取数据库连接池
    const pool = await getConnection();
    
    // 使用连接池执行更新
    await pool.query(
      'UPDATE users SET reset_code = ?, reset_code_expiry = ? WHERE id = ?',
      [resetCode, expiryTime, user.id]
    );

    // 使用用户指定的语言、用户存储的语言偏好或从请求中获取的语言
    const userLanguage = language || user.language || req.language;
    
    // 创建特定语言的翻译函数
    const t = (key, params = {}) => translate(key, userLanguage, params);

    // 发送验证码邮件
    await EmailService.sendMailWithRetry({
      to: email,
      subject: t('auth.email.resetCodeSubject'),
      html: `
        <h1>${t('auth.email.resetPasswordTitle')}</h1>
        <p>${t('auth.email.resetCodeMessage', { code: resetCode })}</p>
        <p>${t('auth.email.resetCodeExpiry')}</p>
        <p>${t('auth.email.footer')}</p>
      `
    });
    
    res.status(200).json({ message: req.t('auth.success.codeSent') });
  } catch (error) {
    console.error('发送重置密码验证码失败:', error.message);
    res.status(500).json({ 
      message: req.t('auth.error.sendCode'),
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// 使用验证码重置密码
router.post('/reset-password-with-code', async (req, res) => {
  try {
    const { email, code, newPassword } = req.body;

    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(404).json({ message: req.t('auth.error.userNotFound') });
    }

    // 验证验证码
    if (!user.reset_code || !user.reset_code_expiry || 
        user.reset_code !== code || 
        new Date() > new Date(user.reset_code_expiry)) {
      return res.status(400).json({ message: req.t('auth.validation.invalidOrExpiredCode') });
    }

    // 更新密码
    await User.updatePassword(user.id, newPassword);

    res.status(200).json({ message: req.t('auth.success.passwordReset') });
  } catch (error) {
    console.error('Reset password error:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({ message: req.t('auth.error.resetPassword') });
  }
});

// 重置密码
router.post('/reset-password', async (req, res) => {
  try {
    const { token, newPassword } = req.body;

    // 验证token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      return res.status(400).json({ message: req.t('auth.validation.resetLinkExpired') });
    }

    // 查找用户
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(400).json({ message: req.t('auth.error.userNotFound') });
    }

    // 验证token是否与数据库中存储的一致
    if (!user.reset_token || user.reset_token !== token) {
      return res.status(400).json({ message: req.t('auth.validation.resetLinkExpired') });
    }

    // 更新密码
    await User.updatePassword(user.id, newPassword);
    // 清除重置token
    await User.updateResetToken(user.id, null);

    res.json({ message: req.t('auth.success.passwordReset') });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({ message: req.t('auth.error.resetPassword') });
  }
});

// 忘记密码路由
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    // 验证邮箱格式
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ message: req.t('auth.validation.invalidEmail') });
    }

    // 查找用户
    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(404).json({ message: req.t('auth.validation.emailNotRegistered') });
    }

    // 生成重置密码的验证码
    const resetCode = generateVerificationCode();
    
    // 保存验证码和过期时间
    await User.update(user.id, {
      reset_code: resetCode,
      reset_code_expiry: new Date(Date.now() + 30 * 60 * 1000) // 30分钟有效期
    });

    // 发送重置密码邮件
    await EmailService.sendMailWithRetry({
      to: email,
      subject: req.t('auth.email.resetCodeSubject'),
      html: `
        <h1>${req.t('auth.email.resetPasswordTitle')}</h1>
        <p>${req.t('auth.email.resetCodeMessage', { code: resetCode })}</p>
        <p>${req.t('auth.email.resetCodeExpiry')}</p>
      `
    });

    res.json({ message: req.t('auth.success.resetCodeSent') });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({ message: req.t('auth.error.sendResetEmail') });
  }
});

// Google 登录路由
router.post('/google', async (req, res) => {
  try {
    // console.log('=== Google Auth Start ==='); 
    const { credential, fingerprint } = req.body;
    // console.log('Google登录请求指纹状态:', fingerprint ? '已提供' : '未提供');
    
    if (fingerprint) {
      // console.log('Google登录请求指纹:', fingerprint.substring(0, 10) + '...');
    }
    
    // 解码 Google Token
    const payload = jwtDecode(credential);
    console.log('Google登录用户信息:', {
      email: payload.email,
      name: payload.name
    });

    // 验证基本信息
    if (!payload.email_verified) {
      throw new Error('Email not verified with Google');
    }

    // 验证令牌是否过期
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      throw new Error('Token has expired');
    }

    // 验证令牌的受众
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const tokenAud = Array.isArray(payload.aud) ? payload.aud : [payload.aud];
    if (!tokenAud.includes(clientId)) {
      console.log('Invalid audience. Token aud:', tokenAud, 'Expected:', clientId);
      throw new Error('Invalid token audience');
    }

    // 获取用户IP和地理位置信息
    const ip = req.headers['x-forwarded-for'] || 
               req.connection.remoteAddress || 
               req.socket.remoteAddress;
    const geo = geoip.lookup(ip);

    // 查找或创建用户
    // console.log('查找已存在的用户:', payload.email);
    let user = await User.findByEmail(payload.email);
    // console.log('用户查找结果:', user ? '已存在' : '未找到');
    
    // 检查是否有相同指纹的账号
    let shouldResetRemainingReads = false;
    let existingUsersWithFingerprint = [];
    
    if (fingerprint) {
      // console.log('开始查找是否有相同指纹的账号:', fingerprint.substring(0, 10) + '...');
      existingUsersWithFingerprint = await User.findByFingerprint(fingerprint);
      
      if (existingUsersWithFingerprint && existingUsersWithFingerprint.length > 0) {
        console.log(`谷歌登录: 发现${existingUsersWithFingerprint.length}个账号使用相同指纹，详情:`, 
          existingUsersWithFingerprint.map(u => ({ id: u.id, email: u.email })));
        
        // 如果是新用户且有其他账号使用相同指纹，则设置阅读次数为0
        if (!user) {
          console.log('新用户将被设置为0次阅读次数');
          shouldResetRemainingReads = true;
        }
      } else {
        console.log('未找到使用相同指纹的账号');
      }
    } else {
      console.log('未提供指纹，跳过指纹查重检查');
    }
    
    if (!user) {
      console.log('用户不存在，创建新用户...');
      // 生成唯一用户名
      const baseUsername = payload.name.toLowerCase().replace(/\s+/g, '');
      const timestamp = Date.now().toString().slice(-4);
      const uniqueUsername = `${baseUsername}${timestamp}`;

      try {
        // 创建新用户
        user = await User.create({
          email: payload.email,
          username: baseUsername,
          avatar: payload.picture,
          authProvider: 'google',
          isEmailVerified: true,
          vipStatus: 'none',
          remainingReads: shouldResetRemainingReads ? 0 : 3,
          country: geo ? geo.country : null,
          ip: ip || null,
          fingerprint: fingerprint || null
        });
        console.log('创建新的Google用户:', {
          userId: user.id,
          country: geo ? geo.country : 'Unknown',
          hasFingerprint: !!fingerprint,
          remainingReads: shouldResetRemainingReads ? 0 : 3
        });
      } catch (createError) {
        // 如果是重复邮箱错误，说明在我们检查和创建之间，另一个请求已经创建了该用户
        if (createError.code === 'ER_DUP_ENTRY' && createError.sqlMessage.includes('email')) {
          console.log('检测到重复邮箱错误，重新查找用户:', payload.email);
          // 重新查找用户，这次应该能找到
          user = await User.findByEmail(payload.email);
          if (!user) {
            console.error('重新查找用户失败，这不应该发生');
            throw new Error('用户创建失败，请重试');
          }
          console.log('成功找到已存在的用户:', user.id);
        } else {
          // 其他错误直接抛出
          throw createError;
        }
      }
    } else {
      // 如果用户已存在，检查并更新country信息
      // console.log('已存在的Google用户:', {
      //   userId: user.id,
      //   oldCountry: user.country || 'None',
      //   detectedCountry: geo ? geo.country : 'Unknown',
      //   hasFingerprint: !!fingerprint
      // });
      
      if (geo && geo.country) {
        // 总是更新country信息，以保持最新
        await User.update(user.id, { 
          country: geo.country,
          ip: ip || null
        });
        user.country = geo.country;
        user.ip = ip || null;
        console.log('Updated Google user country and IP:', {
          userId: user.id,
          newCountry: geo.country,
          ip: ip || 'Unknown'
        });
      } else if (ip) {
        // 即使没有获取到地理位置信息，也更新IP
        await User.update(user.id, { ip: ip });
        user.ip = ip;
        // console.log('Updated Google user IP only:', {
        //   userId: user.id,
        //   ip: ip
        // });
      }
      
      // 如果提供了指纹，更新用户的指纹信息
      if (fingerprint) {
        await User.updateFingerprint(user.id, fingerprint);
        // console.log('Updated Google user fingerprint:', {
        //   userId: user.id
        // });
      }
    }

    // 生成 JWT token
    // console.log('Generating JWT token...');
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    // console.log('Login successful, sending response');
    res.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        remainingReads: user.remaining_reads,
        vipStatus: user.vip_status,
        country: user.country,
        location: user.location,
        ip: user.ip
      }
    });
    // console.log('=== Google Auth End ===');
  } catch (error) {
    console.error('Google auth error:', error);
    console.error('Error stack:', error.stack);
    res.status(401).json({ error: req.t('auth.error.authentication'), details: error.message });
  }
});

// 获取用户IP和对应语言
router.get('/ip-language', async (req, res) => {
  try {
    // 获取客户端IP
    const ip = req.headers['x-forwarded-for'] || 
               req.connection.remoteAddress || 
               req.socket.remoteAddress;
               
    // console.log('Client IP:', ip);
    
    // 使用 geoip-lite 获取地理位置信息
    const geo = geoip.lookup(ip);
    // console.log('GeoIP Info:', geo);
    
    // 根据国家/地区代码确定语言
    let language = 'zh-CN'; // 默认使用简体中文
    
    if (geo) {
      switch(geo.country) {
        case 'JP':
          language = 'ja';
          break;
        case 'TW':
        case 'HK':
        case 'MO':
          language = 'zh-TW';
          break;
        case 'US':
        case 'GB':
        case 'AU':
        case 'CA':
          language = 'en';
          break;
        default:
          // 如果是其他中国地区，使用简体中文
          if (geo.country === 'CN') {
            language = 'zh-CN';
          }
          break;
      }
    }
    
    // console.log('Detected Language:', language);
    
    res.json({
      ip,
      country: geo ? geo.country : null,
      language
    });
  } catch (error) {
    // console.error('Error getting IP language:', error);
    // res.status(500).json({ message: req.t('auth.error.getIpLanguage') });
  }
});

// 更新用户信息
router.put('/user/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const updateData = req.body;

    // 确保用户只能更新自己的信息
    if (userId !== req.user.userId) {
      console.log('Unauthorized update attempt:', {
        requestUserId: userId,
        tokenUserId: req.user.userId
      });
      return res.status(403).json({ message: req.t('auth.error.unauthorizedUpdate') });
    }

    await User.update(userId, updateData);

    // 获取更新后的用户信息
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: req.t('auth.error.userNotFound') });
    }

    // 返回更新后的用户信息
    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      gender: user.gender,
      age: user.age,
      remainingReads: user.remaining_reads,
      vipStatus: user.vip_status,
      vipType: user.vip_type,
      vipEndDate: user.vip_end_date,
      isEmailVerified: user.is_email_verified === 1,
      userProfile: user.user_profile,
      whether_paypal: user.whether_paypal,
      country: user.country,
      birthday: user.birthday,
      location: user.location
    });
  } catch (error) {
    console.error('Update user error:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({ message: req.t('auth.error.updateUser') });
  }
});

// 验证重置密码验证码
router.post('/verify-reset-code', async (req, res) => {
  try {
    const { email, code } = req.body;
    
    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(404).json({ message: req.t('auth.error.userNotFound') });
    }

    // 验证验证码
    if (!user.reset_code || !user.reset_code_expiry || 
        user.reset_code !== code || 
        new Date() > new Date(user.reset_code_expiry)) {
      return res.status(400).json({ message: req.t('auth.validation.invalidOrExpiredCode') });
    }

    res.status(200).json({ message: req.t('auth.success.codeVerified') });
  } catch (error) {
    console.error('Verify reset code error:', error);
    res.status(500).json({ message: req.t('auth.error.verification') });
  }
});

// 重发验证码路由
router.post('/resend-verification', async (req, res) => {
  try {
    const { email, userId, language } = req.body;
    // console.log('重发验证码请求:', { email, userId, language });
    
    // 验证邮箱格式
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    if (!emailRegex.test(email)) {
      console.log('邮箱格式验证失败:', email);
      return res.status(400).json({ message: req.t('auth.validation.invalidEmail') });
    }
    
    // 检查邮箱是否已经在正式用户表中
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      console.log('邮箱已存在于正式用户表:', email);
      return res.status(400).json({ message: req.t('auth.validation.emailExists') });
    }
    
    // 优先使用userId查找临时用户，如果没有提供userId则使用email
    let tempUser;
    if (userId) {
      console.log('通过userId查找临时用户:', userId);
      tempUser = await TempUser.findById(userId);
      console.log('找到的临时用户:', tempUser ? { id: tempUser.id, email: tempUser.email } : null);
      // 验证找到的用户email是否匹配
      if (tempUser && tempUser.email !== email) {
        console.log('用户ID与邮箱不匹配:', { userId, tempUserEmail: tempUser.email, requestEmail: email });
        return res.status(400).json({ message: req.t('auth.validation.userIdEmailMismatch') });
      }
    } else {
      // console.log('通过email查找临时用户:', email);
      tempUser = await TempUser.findByEmail(email);
      // console.log('找到的临时用户:', tempUser ? { id: tempUser.id, email: tempUser.email } : null);
    }
    
    if (!tempUser) {
      // console.log('未找到临时用户，返回404:', { email, userId });
      return res.status(404).json({ message: req.t('auth.validation.emailNotExists') });
    }
    
    // 生成新的验证码
    const verificationCode = generateVerificationCode();
    // console.log('生成新验证码:', verificationCode);
    
    // 更新临时用户的验证码和过期时间
    await TempUser.update(tempUser.id, {
      verification_code: verificationCode,
      verification_code_expiry: new Date(Date.now() + 30 * 60 * 1000) // 30分钟有效期
    });
    // console.log('已更新临时用户的验证码和过期时间:', tempUser.id);
    
    // 检查更新是否成功
    // const updatedTempUser = await TempUser.findById(tempUser.id);
    // if (updatedTempUser) {
    //   console.log('更新后的临时用户数据:', {
    //     id: updatedTempUser.id,
    //     email: updatedTempUser.email,
    //     verification_code: updatedTempUser.verification_code,
    //     verification_code_expiry: updatedTempUser.verification_code_expiry
    //   });
    // } else {
    //   console.error('更新后找不到临时用户:', tempUser.id);
    // }
    
    // 使用用户指定的语言或从请求中获取的语言
    const userLanguage = language || tempUser.language || req.language;
    const { translate } = require('../i18n');
    
    // 创建特定语言的翻译函数
    const t = (key, params = {}) => translate(key, userLanguage, params);
    
    // 发送验证邮件
    // console.log('验证邮件服务器连接...');
    // await transporter.verify();
    const mailOptions = {
      // 移除from字段，让EmailService自动设置
      to: email,
      subject: t('auth.email.registerSubject'),
      html: `
        <h1>${t('auth.email.registerTitle')}</h1>
        <p>${t('auth.email.registerCodeMessage')} <strong>${verificationCode}</strong></p>
        <p>${t('auth.email.registerCodeExpiry')}</p>
        <p>${t('auth.email.footer')}</p>
      `
    };
    // console.log('准备发送邮件:', { to: email, subject: mailOptions.subject });
    const info = await EmailService.sendMailWithRetry(mailOptions);
    // console.log('邮件发送成功:', { messageId: info.messageId });
    
    res.status(200).json({ 
      message: req.t('auth.success.codeSent'),
      userId: tempUser.id // 返回userId确保前端可以使用正确的ID
    });
  } catch (error) {
    console.error('Resend verification error:', error);
    res.status(500).json({ message: req.t('auth.error.sendCode') });
  }
});

// 测试邮件发送（仅用于开发环境）
if (process.env.NODE_ENV === 'development') {
  router.post('/test-email', async (req, res) => {
    try {
      const { email, language } = req.body;
      
      if (!email) {
        return res.status(400).json({ message: req.t('auth.validation.provideEmail') });
      }
      
      // 验证邮箱格式
      const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ message: req.t('auth.validation.invalidEmail') });
      }
      
      // 使用用户指定的语言或从请求中获取的语言
      const userLanguage = language || req.language;
      
      // 创建特定语言的翻译函数
      const t = (key, params = {}) => translate(key, userLanguage, params);
      
      // 发送测试邮件
      // await transporter.verify();
      await EmailService.sendMailWithRetry({
        // 移除from字段，让EmailService自动设置
        to: email,
        subject: t('auth.email.testSubject'),
        html: `
          <h1>${t('auth.email.testTitle')}</h1>
          <p>${t('auth.email.testMessage')}</p>
          <p>${t('auth.email.timestamp', { time: new Date().toISOString() })}</p>
          <p>${t('auth.email.footer')}</p>
        `
      });
      
      console.log(`测试邮件已发送至 ${email}，使用语言: ${userLanguage}`);
      res.status(200).json({ message: req.t('auth.success.testEmailSent') });
    } catch (error) {
      console.error('测试邮件发送失败:', error.message);
      res.status(500).json({ 
        message: req.t('auth.error.testEmailFailed'), 
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  });
}

// 检查用户是否有内部折扣权限
router.get('/check-discount', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 检查用户是否有内部权限
    const hasInternalPrivilege = await User.hasInternalPrivilege(userId);
    
    // 如果用户有内部权限，还需检查是否已使用过折扣
    if (hasInternalPrivilege) {
      const hasUsedDiscount = await User.hasUsedDiscount(userId);
      
      // 如果已使用过折扣，则不再提供折扣
      if (hasUsedDiscount) {
        return res.json({
          hasDiscount: false,
          discountRate: 1.0,
          discountType: 'none',
          hasUsedDiscount: true
        });
      }
      
      return res.json({
        hasDiscount: true,
        discountRate: 0.8, // 有内部权限且未使用过折扣则享受80%折扣
        discountType: 'internal',
        hasUsedDiscount: false
      });
    }
    
    res.json({
      hasDiscount: false,
      discountRate: 1.0, // 无内部权限无折扣
      discountType: 'none',
      hasUsedDiscount: false
    });
  } catch (error) {
    console.error('检查用户折扣权限失败:', error);
    res.status(500).json({ error: '检查用户折扣权限失败' });
  }
});

module.exports = router; 