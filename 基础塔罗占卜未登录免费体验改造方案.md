# 基础塔罗占卜未登录免费体验改造方案

## 一、需求分析

### 当前状态
- 新用户注册后赠送3次免费占卜次数（非重复指纹下）
- 只有登录状态下才可以使用基础塔罗占卜
- 登录后需要判断是否有足够的次数或者是否为VIP

### 目标状态
- 未登录状态下可以使用基础塔罗占卜，每个人只有1次免费机会
- 使用浏览器指纹检查来判断是否已使用过免费机会
- 用户进行一次免费占卜后，剩余次数变为0
- 此时提示"登录后获得更多占卜次数"，并提供导航至登录页面的按钮
- 用户注册登录后额外赠送2次免费占卜（总共3次，减去未登录时使用的1次）

## 二、技术方案分析

### 2.1 数据库设计方案

#### 方案：新增独立表（推荐）
创建新表 `anonymous_divination_records` 来记录未登录占卜：

```sql
CREATE TABLE `anonymous_divination_records` (
  `id` varchar(36) NOT NULL PRIMARY KEY,
  `browser_fingerprint` text NOT NULL COMMENT '浏览器指纹',
  `session_id` varchar(36) NOT NULL COMMENT '占卜会话ID',
  `question` text NOT NULL COMMENT '占卜问题',
  `spread_id` varchar(36) DEFAULT NULL COMMENT '牌阵ID',
  `spread_name` varchar(255) DEFAULT NULL COMMENT '牌阵名称',
  `selected_cards` json DEFAULT NULL COMMENT '选中的卡牌',
  `reading_result` json DEFAULT NULL COMMENT '占卜结果',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ip_address` varchar(100) DEFAULT NULL COMMENT 'IP地址',
  INDEX `idx_fingerprint` (`browser_fingerprint`(255)),
  INDEX `idx_session_id` (`session_id`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='匿名占卜记录表';
```

**优势：**
- 数据隔离，不影响现有用户数据结构
- 便于统计和管理匿名用户行为
- 可以独立设置数据保留策略
- 扩展性好，后续可以添加更多匿名用户相关字段


### 2.2 浏览器指纹方案

#### 当前指纹实现分析
- 使用 `@fingerprintjs/fingerprintjs` 库
- 存储在用户表的 `browser_fingerprints` 字段中

#### 未登录指纹检查逻辑
1. 获取当前浏览器指纹
2. 查询 `anonymous_divination_records` 表检查是否已存在该指纹的记录
3. 如果存在记录，则该用户已使用过免费机会
4. 如果不存在记录，则可以进行免费占卜

### 2.3 权限控制逻辑重构

#### 当前权限检查流程
```
用户访问基础塔罗占卜 → 检查登录状态 → 未登录则显示登录提示 → 登录后检查VIP状态和剩余次数
```

#### 新的权限检查流程
```
用户访问基础塔罗占卜 → 检查登录状态
├─ 已登录 → 检查VIP状态和剩余次数
└─ 未登录 → 检查浏览器指纹是否已使用免费机会
   ├─ 已使用 → 显示"登录后获得更多占卜次数"提示
   └─ 未使用 → 允许进行1次免费占卜
```

## 三、具体实现方案

### 3.1 前端改造

#### 3.1.1 权限检查组件修改

基于代码分析，需要修改以下组件的权限检查逻辑以支持匿名用户：

**1. Home.tsx - 首页入口**
- **当前API调用**:
  - `getReaderVotes(reader.id)` - 获取占卜师投票数
  - `getUserVotedReaders()` - 获取用户已投票的占卜师列表
  - `checkRavenUsed()` - 检查用户是否使用过渡鸦占卜师
- **权限检查**: `checkUserPermission()` 检查登录状态和剩余次数
- **修改方案**: 权限检查逻辑需要支持匿名用户，允许未登录用户选择基础占卜师。但投票依然保持登录要求

**2. AskQuestion.tsx - 问题输入页面**
- **当前API调用**: `createSession(question)` → `/api/session` (POST)
- **后端中间件**: `authenticateToken` (必须登录)
- **修改方案**: 前端权限检查支持匿名用户，后端API改为 `optionalAuthenticateToken`

**3. SpreadSelection.tsx - 牌阵选择页面**
- **当前API调用**:
  - `/api/spread-recommendation/` (POST) - 牌阵推荐
  - `updateSession(sessionId, {...})` → `/api/session/${sessionId}` (PATCH)
- **后端中间件**: `authenticateToken` (必须登录)
- **修改方案**: 两个API都需要改为支持匿名用户

**4. TarotCardSelection.tsx - 抽牌页面**
- **当前API调用**:
  - `updateSession(sessionId, {...})` → `/api/session/${sessionId}` (PATCH)
- **权限检查**: 前端检查用户登录和VIP状态
- **修改方案**: 前后端都需要修改以支持匿名用户

**5. TarotResultPage.tsx - 结果页面**
- **当前API调用**:
  - `/api/reading/shared/${sessionId}` (GET) - 获取分享的解读
  - `generateTarotReading()` → `/api/tarot` (POST) - 生成塔罗解读
  - `checkUserDeepAnalysisUsage()` - 检查深度分析使用情况
  - `checkUserFollowupUsage()` - 检查追问使用情况
- **修改方案**: 核心的塔罗解读API需要支持匿名用户，深度分析和追问保持登录要求。分享和评价保持登录要求。

#### 3.1.2 剩余次数显示组件
创建新的组件来显示不同状态下的剩余次数：

```typescript
interface RemainingReadsDisplayProps {
  user: User | null;
  anonymousReadsUsed: boolean;
  onLoginClick: () => void;
}

const RemainingReadsDisplay: React.FC<RemainingReadsDisplayProps> = ({
  user,
  anonymousReadsUsed,
  onLoginClick
}) => {
  if (!user) {
    // 未登录状态
    if (anonymousReadsUsed) {
      return (
        <div className="text-center p-4">
          <p>您的免费占卜次数已用完</p>
          <button onClick={onLoginClick} className="btn-primary">
            登录后获得更多占卜次数
          </button>
        </div>
      );
    } else {
      return <div>剩余免费占卜次数：1次</div>;
    }
  }

  // 已登录状态 - 使用现有逻辑
  if (user.vipStatus === 'active') {
    return <div>VIP用户 - 无限次占卜</div>;
  }

  return <div>剩余占卜次数：{user.remainingReads}次</div>;
};
```

#### 3.1.3 浏览器指纹检查服务
创建专门的服务来处理匿名用户指纹检查：

```typescript
// src/services/anonymousService.ts
export const checkAnonymousEligibility = async (fingerprint: string): Promise<{
  canUse: boolean;
  hasUsed: boolean;
}> => {
  const response = await axiosInstance.post('/api/anonymous/check-eligibility', {
    fingerprint
  });
  return response.data;
};

export const recordAnonymousDivination = async (data: {
  fingerprint: string;
  sessionId: string;
  question: string;
  spreadId: string;
  spreadName: string;
  selectedCards: any[];
  readingResult: any;
}) => {
  const response = await axiosInstance.post('/api/anonymous/record', data);
  return response.data;
};
```


### 3.2 后端改造

#### 3.2.1 新增匿名占卜API路由

```javascript
// server/routes/anonymous.js
const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../config/database');

// 检查匿名用户是否可以使用免费占卜
router.post('/check-eligibility', async (req, res) => {
  try {
    const { fingerprint } = req.body;
    
    if (!fingerprint) {
      return res.status(400).json({ error: '缺少浏览器指纹' });
    }

    const pool = await getConnection();
    const [rows] = await pool.query(
      'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE browser_fingerprint = ?',
      [fingerprint]
    );

    const hasUsed = rows[0].count > 0;
    
    res.json({
      canUse: !hasUsed,
      hasUsed: hasUsed
    });
  } catch (error) {
    console.error('检查匿名用户资格时出错:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 记录匿名占卜
router.post('/record', async (req, res) => {
  try {
    const {
      fingerprint,
      sessionId,
      question,
      spreadId,
      spreadName,
      selectedCards,
      readingResult
    } = req.body;

    const pool = await getConnection();
    
    // 检查是否已经记录过
    const [existing] = await pool.query(
      'SELECT id FROM anonymous_divination_records WHERE browser_fingerprint = ?',
      [fingerprint]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: '该用户已使用过免费占卜机会' });
    }

    // 记录匿名占卜
    const recordId = uuidv4();
    await pool.query(
      `INSERT INTO anonymous_divination_records 
       (id, browser_fingerprint, session_id, question, spread_id, spread_name, 
        selected_cards, reading_result, ip_address) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        recordId,
        fingerprint,
        sessionId,
        question,
        spreadId,
        spreadName,
        JSON.stringify(selectedCards),
        JSON.stringify(readingResult),
        req.ip
      ]
    );

    res.json({ success: true, recordId });
  } catch (error) {
    console.error('记录匿名占卜时出错:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router;
```

#### 3.2.2 修改现有API的权限检查

基于代码分析，以下API需要修改权限检查逻辑以支持匿名用户：

##### 需要修改的核心API

**1. server/routes/session.js - 会话管理API**
```javascript
// 当前使用: authenticateToken
// 需要改为: optionalAuthenticateToken
router.post('/', optionalAuthenticateToken, async (req, res) => {
  // 添加匿名用户处理逻辑
  if (!req.user) {
    // 匿名用户逻辑：检查浏览器指纹是否已使用免费机会
    const { fingerprint } = req.body;
    const hasUsed = await checkAnonymousUsage(fingerprint);
    if (hasUsed) {
      return res.status(403).json({ error: '您的免费占卜次数已用完，请登录获取更多次数' });
    }
  }
  // 原有的登录用户逻辑...
});

router.patch('/:sessionId', optionalAuthenticateToken, async (req, res) => {
  // 添加匿名用户会话更新逻辑
});
```

**2. server/routes/spreadRecommendation.js - 牌阵推荐API**
```javascript
// 当前使用: authenticateToken
// 需要改为: optionalAuthenticateToken
router.post('/', optionalAuthenticateToken, async (req, res) => {
  // 支持匿名用户的牌阵推荐
});
```

**3. server/routes/tarot.js - 基础塔罗占卜API**
```javascript
// 当前使用: authenticateToken
// 需要改为: optionalAuthenticateToken
router.post('/', optionalAuthenticateToken, async (req, res) => {
  if (!req.user) {
    // 匿名用户权限检查和指纹验证逻辑
    const { fingerprint } = req.body;
    const eligibility = await checkAnonymousEligibility(fingerprint);
    if (!eligibility.canUse) {
      return res.status(403).json({ error: '您的免费占卜次数已用完，请登录获取更多次数' });
    }
  }
  // 原有的登录用户逻辑...
});
```

##### 保持登录要求的API

以下API应该继续要求登录，不支持匿名用户：
- **深度分析API** (`/api/deepanalysis`) - 高级功能
- **追问功能API** (`/api/followup`) - 高级功能
- **语音功能API** (`/api/tts`, `/api/pro-tts`) - 高级功能
- **用户相关API** (`/api/user/*`) - 用户数据相关
- **是否塔罗API** (`/api/yes-no-tarot`) - 独立功能模块
- **每日运势API** - 独立功能模块

#### 3.2.3 用户注册逻辑修改

修改用户注册时的免费次数赠送逻辑：

```javascript
// 在注册成功后检查是否有匿名占卜记录
const checkAndAdjustFreeReads = async (userId, fingerprint) => {
  const pool = await getConnection();
  
  // 检查该指纹是否有匿名占卜记录
  const [anonymousRecords] = await pool.query(
    'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE browser_fingerprint = ?',
    [fingerprint]
  );
  
  const hasAnonymousRecord = anonymousRecords[0].count > 0;
  
  // 如果有匿名记录，则只赠送2次（总共3次减去已使用的1次）
  // 如果没有匿名记录，则赠送3次
  const freeReads = hasAnonymousRecord ? 2 : 3;
  
  await pool.query(
    'UPDATE users SET remaining_reads = ? WHERE id = ?',
    [freeReads, userId]
  );
  
  return freeReads;
};
```
