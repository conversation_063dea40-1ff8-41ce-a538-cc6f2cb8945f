/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = guest_divination_records   */
/******************************************/
CREATE TABLE `guest_divination_records` (
  `id` varchar(36) NOT NULL,
  `fingerprint` varchar(255) NOT NULL COMMENT '浏览器指纹',
  `session_id` varchar(36) NOT NULL COMMENT '占卜会话ID',
  `divination_type` varchar(50) NOT NULL COMMENT '占卜类型：basic-tarot/yes-no-tarot/daily-fortune',
  `question` text COMMENT '用户问题',
  `selected_cards` json COMMENT '选中的卡牌信息',
  `spread_info` json COMMENT '牌阵信息',
  `result` text COMMENT '占卜结果',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `ip_address` varchar(100) DEFAULT NULL COMMENT 'IP地址（预留字段）',
  PRIMARY KEY (`id`),
  KEY `idx_fingerprint` (`fingerprint`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='未登录用户占卜记录表';
