import axiosInstance from '../utils/axios';

export interface AnonymousEligibility {
  canUse: boolean;
  hasUsed: boolean;
  fingerprintUsed?: boolean;
  ipUsed?: boolean;
  reason?: 'fingerprint' | 'ip' | null;
}

export interface AnonymousDivinationData {
  fingerprint: string;
  sessionId: string;
  question: string;
  spreadId: string;
  spreadName: string;
  selectedCards: any[];
  readingResult: any;
}

/**
 * 检查匿名用户是否可以使用免费占卜
 * @param fingerprint 浏览器指纹
 * @returns 是否可以使用免费占卜的信息
 */
export const checkAnonymousEligibility = async (fingerprint: string): Promise<AnonymousEligibility> => {
  try {
    const response = await axiosInstance.post('/api/anonymous/check-eligibility', {
      fingerprint
    });
    return response.data;
  } catch (error) {
    console.error('检查匿名用户资格失败:', error);
    // 出错时默认不允许使用，确保安全
    return {
      canUse: false,
      hasUsed: true
    };
  }
};

/**
 * 记录匿名用户的占卜信息
 * @param data 占卜数据
 * @returns 记录结果
 */
export const recordAnonymousDivination = async (data: AnonymousDivinationData) => {
  try {
    const response = await axiosInstance.post('/api/anonymous/record', data);
    return response.data;
  } catch (error) {
    console.error('记录匿名占卜失败:', error);
    throw error;
  }
};

/**
 * 获取匿名占卜统计信息（管理员用）
 * @returns 统计信息
 */
export const getAnonymousStats = async () => {
  try {
    const response = await axiosInstance.get('/api/anonymous/stats');
    return response.data;
  } catch (error) {
    console.error('获取匿名占卜统计失败:', error);
    throw error;
  }
};

/**
 * 检查用户是否为匿名用户（基于是否有token）
 * @returns 是否为匿名用户
 */
export const isAnonymousUser = (): boolean => {
  const token = localStorage.getItem('token');
  return !token;
};

/**
 * 获取或生成匿名用户的临时标识
 * @returns 临时标识
 */
export const getAnonymousIdentifier = (): string => {
  let identifier = localStorage.getItem('anonymousId');
  if (!identifier) {
    identifier = `anon_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    localStorage.setItem('anonymousId', identifier);
  }
  return identifier;
};

/**
 * 清除匿名用户相关的本地存储数据
 */
export const clearAnonymousData = (): void => {
  localStorage.removeItem('anonymousId');
  // 可以根据需要清除其他匿名用户相关数据
};

/**
 * 检查是否应该显示登录引导
 * @param fingerprint 浏览器指纹
 * @returns 是否应该显示登录引导
 */
export const shouldShowLoginPrompt = async (fingerprint: string): Promise<boolean> => {
  try {
    const eligibility = await checkAnonymousEligibility(fingerprint);
    return eligibility.hasUsed && !localStorage.getItem('token');
  } catch (error) {
    console.error('检查登录引导状态失败:', error);
    return false;
  }
};
